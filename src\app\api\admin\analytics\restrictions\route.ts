import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { db } from '@/lib/db';

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id || session.user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const period = searchParams.get('period') || '30'; // days
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');

    // Calculate date range
    let dateCondition = '';
    let dateParams = [];

    if (startDate && endDate) {
      dateCondition = 'WHERE created_at BETWEEN ? AND ?';
      dateParams = [startDate, endDate];
    } else {
      dateCondition = 'WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)';
      dateParams = [parseInt(period)];
    }

    // 1. Overall Statistics
    const [overallStats] = await db.execute(`
      SELECT 
        COUNT(*) as total_restrictions,
        SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_restrictions,
        SUM(CASE WHEN status = 'lifted' THEN 1 ELSE 0 END) as lifted_restrictions,
        SUM(CASE WHEN is_permanent = TRUE THEN 1 ELSE 0 END) as permanent_restrictions,
        AVG(CASE 
          WHEN status = 'lifted' AND lifted_at IS NOT NULL 
          THEN TIMESTAMPDIFF(HOUR, start_date, lifted_at) 
          ELSE NULL 
        END) as avg_duration_hours
      FROM user_restrictions
      ${dateCondition}
    `, dateParams);

    // 2. Restrictions by Type
    const [restrictionsByType] = await db.execute(`
      SELECT 
        restriction_type,
        COUNT(*) as count,
        SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_count
      FROM user_restrictions
      ${dateCondition}
      GROUP BY restriction_type
      ORDER BY count DESC
    `, dateParams);

    // 3. Restrictions by Reason Category
    const [restrictionsByReason] = await db.execute(`
      SELECT 
        reason_category,
        COUNT(*) as count,
        AVG(CASE 
          WHEN status = 'lifted' AND lifted_at IS NOT NULL 
          THEN TIMESTAMPDIFF(DAY, start_date, lifted_at) 
          ELSE NULL 
        END) as avg_duration_days
      FROM user_restrictions
      ${dateCondition}
      GROUP BY reason_category
      ORDER BY count DESC
    `, dateParams);

    // 4. Restrictions by Severity
    const [restrictionsBySeverity] = await db.execute(`
      SELECT 
        severity,
        COUNT(*) as count,
        SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_count
      FROM user_restrictions
      ${dateCondition}
      GROUP BY severity
      ORDER BY 
        CASE severity 
          WHEN 'critical' THEN 1 
          WHEN 'severe' THEN 2 
          WHEN 'moderate' THEN 3 
          WHEN 'minor' THEN 4 
        END
    `, dateParams);

    // 5. Daily Restriction Trends
    const [dailyTrends] = await db.execute(`
      SELECT 
        DATE(created_at) as date,
        COUNT(*) as restrictions_applied,
        SUM(CASE WHEN status = 'lifted' AND DATE(lifted_at) = DATE(created_at) THEN 1 ELSE 0 END) as restrictions_lifted
      FROM user_restrictions
      ${dateCondition}
      GROUP BY DATE(created_at)
      ORDER BY date DESC
      LIMIT 30
    `, dateParams);

    // 6. Admin Activity
    const [adminActivity] = await db.execute(`
      SELECT 
        u.first_name,
        u.last_name,
        COUNT(*) as restrictions_applied,
        AVG(CASE 
          WHEN r.status = 'lifted' AND r.lifted_at IS NOT NULL 
          THEN TIMESTAMPDIFF(DAY, r.start_date, r.lifted_at) 
          ELSE NULL 
        END) as avg_restriction_duration
      FROM user_restrictions r
      JOIN users u ON r.applied_by_admin_id = u.user_id
      ${dateCondition}
      GROUP BY r.applied_by_admin_id, u.first_name, u.last_name
      ORDER BY restrictions_applied DESC
      LIMIT 10
    `, dateParams);

    // 7. Appeal Statistics
    const [appealStats] = await db.execute(`
      SELECT 
        COUNT(*) as total_appeals,
        SUM(CASE WHEN status = 'approved' THEN 1 ELSE 0 END) as approved_appeals,
        SUM(CASE WHEN status = 'denied' THEN 1 ELSE 0 END) as denied_appeals,
        SUM(CASE WHEN status IN ('submitted', 'under_review') THEN 1 ELSE 0 END) as pending_appeals,
        AVG(CASE 
          WHEN reviewed_at IS NOT NULL 
          THEN TIMESTAMPDIFF(HOUR, submitted_at, reviewed_at) 
          ELSE NULL 
        END) as avg_review_time_hours
      FROM restriction_appeals a
      JOIN user_restrictions r ON a.restriction_id = r.id
      ${dateCondition.replace('created_at', 'a.submitted_at')}
    `, dateParams);

    // 8. User Behavior Patterns
    const [userPatterns] = await db.execute(`
      SELECT 
        COUNT(DISTINCT user_id) as unique_restricted_users,
        COUNT(*) / COUNT(DISTINCT user_id) as avg_restrictions_per_user,
        SUM(CASE WHEN restriction_count > 1 THEN 1 ELSE 0 END) as repeat_offenders
      FROM (
        SELECT 
          user_id,
          COUNT(*) as restriction_count
        FROM user_restrictions
        ${dateCondition}
        GROUP BY user_id
      ) user_restriction_counts
    `, dateParams);

    // 9. Report to Restriction Conversion
    const [reportConversion] = await db.execute(`
      SELECT 
        COUNT(DISTINCT r.id) as total_reports,
        COUNT(DISTINCT res.related_report_id) as reports_leading_to_restrictions,
        (COUNT(DISTINCT res.related_report_id) / COUNT(DISTINCT r.id)) * 100 as conversion_rate
      FROM user_reports r
      LEFT JOIN user_restrictions res ON r.id = res.related_report_id
      ${dateCondition.replace('created_at', 'r.created_at')}
    `, dateParams);

    // 10. Effectiveness Metrics
    const [effectivenessMetrics] = await db.execute(`
      SELECT 
        restriction_type,
        COUNT(*) as total_applied,
        SUM(CASE WHEN status = 'lifted' THEN 1 ELSE 0 END) as lifted_count,
        SUM(CASE 
          WHEN status = 'lifted' AND lifted_at > DATE_ADD(start_date, INTERVAL 7 DAY)
          THEN 1 ELSE 0 
        END) as effective_restrictions,
        AVG(CASE 
          WHEN status = 'lifted' 
          THEN TIMESTAMPDIFF(DAY, start_date, lifted_at) 
          ELSE NULL 
        END) as avg_duration_days
      FROM user_restrictions
      ${dateCondition}
      GROUP BY restriction_type
    `, dateParams);

    // Format response
    const analytics = {
      period: {
        days: parseInt(period),
        startDate: startDate || null,
        endDate: endDate || null
      },
      overview: {
        totalRestrictions: (overallStats as any)[0].total_restrictions,
        activeRestrictions: (overallStats as any)[0].active_restrictions,
        liftedRestrictions: (overallStats as any)[0].lifted_restrictions,
        permanentRestrictions: (overallStats as any)[0].permanent_restrictions,
        averageDurationHours: parseFloat((overallStats as any)[0].avg_duration_hours || 0).toFixed(1)
      },
      restrictionsByType: restrictionsByType as any[],
      restrictionsByReason: restrictionsByReason as any[],
      restrictionsBySeverity: restrictionsBySeverity as any[],
      dailyTrends: (dailyTrends as any[]).reverse(), // Show oldest to newest
      adminActivity: adminActivity as any[],
      appeals: {
        totalAppeals: (appealStats as any)[0].total_appeals,
        approvedAppeals: (appealStats as any)[0].approved_appeals,
        deniedAppeals: (appealStats as any)[0].denied_appeals,
        pendingAppeals: (appealStats as any)[0].pending_appeals,
        averageReviewTimeHours: parseFloat((appealStats as any)[0].avg_review_time_hours || 0).toFixed(1),
        approvalRate: (appealStats as any)[0].total_appeals > 0 ? 
          ((appealStats as any)[0].approved_appeals / (appealStats as any)[0].total_appeals * 100).toFixed(1) : 0
      },
      userBehavior: {
        uniqueRestrictedUsers: (userPatterns as any)[0].unique_restricted_users,
        averageRestrictionsPerUser: parseFloat((userPatterns as any)[0].avg_restrictions_per_user || 0).toFixed(1),
        repeatOffenders: (userPatterns as any)[0].repeat_offenders
      },
      reportConversion: {
        totalReports: (reportConversion as any)[0].total_reports,
        reportsLeadingToRestrictions: (reportConversion as any)[0].reports_leading_to_restrictions,
        conversionRate: parseFloat((reportConversion as any)[0].conversion_rate || 0).toFixed(1)
      },
      effectiveness: effectivenessMetrics as any[]
    };

    return NextResponse.json({ analytics });

  } catch (error) {
    console.error('Error fetching restriction analytics:', error);
    return NextResponse.json(
      { error: 'Failed to fetch analytics' },
      { status: 500 }
    );
  }
}
