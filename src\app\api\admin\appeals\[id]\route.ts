import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { db } from '@/lib/db';
import { logAdminAction } from '@/services/auditService';
import { sendAdminNotification } from '@/utils/notificationService';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id || session.user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    const appealId = parseInt(params.id);

    // Get detailed appeal information
    const [appeals] = await db.execute(`
      SELECT 
        a.*,
        u.first_name,
        u.last_name,
        u.email,
        u.role,
        r.restriction_type,
        r.reason_category,
        r.custom_reason,
        r.severity,
        r.start_date,
        r.end_date,
        r.is_permanent,
        admin.first_name as admin_first_name,
        admin.last_name as admin_last_name
      FROM restriction_appeals a
      JOIN users u ON a.user_id = u.user_id
      JOIN user_restrictions r ON a.restriction_id = r.id
      LEFT JOIN users admin ON a.assigned_admin_id = admin.user_id
      WHERE a.id = ?
    `, [appealId]);

    if (!Array.isArray(appeals) || appeals.length === 0) {
      return NextResponse.json(
        { error: 'Appeal not found' },
        { status: 404 }
      );
    }

    const appeal = appeals[0] as any;

    // Get communication history for this appeal
    const [communications] = await db.execute(`
      SELECT 
        c.*,
        u.first_name,
        u.last_name,
        u.role
      FROM appeal_communications c
      JOIN users u ON c.sender_id = u.user_id
      WHERE c.appeal_id = ?
      ORDER BY c.created_at ASC
    `, [appealId]);

    // Get user's appeal history
    const [appealHistory] = await db.execute(`
      SELECT 
        id,
        restriction_id,
        status,
        submitted_at,
        admin_response
      FROM restriction_appeals
      WHERE user_id = ? AND id != ?
      ORDER BY submitted_at DESC
      LIMIT 5
    `, [appeal.user_id, appealId]);

    // Format the response
    const formattedAppeal = {
      id: appeal.id,
      restrictionId: appeal.restriction_id,
      user: {
        id: appeal.user_id,
        name: `${appeal.first_name} ${appeal.last_name}`,
        email: appeal.email,
        role: appeal.role
      },
      restriction: {
        type: appeal.restriction_type,
        reasonCategory: appeal.reason_category,
        customReason: appeal.custom_reason,
        severity: appeal.severity,
        startDate: appeal.start_date,
        endDate: appeal.end_date,
        isPermanent: Boolean(appeal.is_permanent)
      },
      appealReason: appeal.appeal_reason,
      supportingDocuments: appeal.supporting_documents ? JSON.parse(appeal.supporting_documents) : null,
      status: appeal.status,
      priority: appeal.priority,
      assignedAdmin: appeal.admin_first_name ? {
        id: appeal.assigned_admin_id,
        name: `${appeal.admin_first_name} ${appeal.admin_last_name}`
      } : null,
      adminResponse: appeal.admin_response,
      internalNotes: appeal.internal_notes,
      escalationReason: appeal.escalation_reason,
      submittedAt: appeal.submitted_at,
      reviewedAt: appeal.reviewed_at,
      decisionAt: appeal.decision_at,
      appealDeadline: appeal.appeal_deadline,
      responseDeadline: appeal.response_deadline,
      createdAt: appeal.created_at,
      updatedAt: appeal.updated_at,
      communications: (communications as any[]).map(comm => ({
        id: comm.id,
        sender: {
          id: comm.sender_id,
          name: `${comm.first_name} ${comm.last_name}`,
          role: comm.role
        },
        senderType: comm.sender_type,
        message: comm.message,
        attachments: comm.attachments ? JSON.parse(comm.attachments) : null,
        isInternal: Boolean(comm.is_internal),
        createdAt: comm.created_at
      })),
      appealHistory: appealHistory as any[]
    };

    return NextResponse.json({ appeal: formattedAppeal });

  } catch (error) {
    console.error('Error fetching appeal details:', error);
    return NextResponse.json(
      { error: 'Failed to fetch appeal details' },
      { status: 500 }
    );
  }
}

// Update appeal status and respond
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id || session.user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    const appealId = parseInt(params.id);
    const adminId = parseInt(session.user.id);
    const { action, response, internalNotes, escalationReason } = await request.json();

    // Get current appeal data
    const [currentAppeal] = await db.execute(`
      SELECT a.*, r.user_id as restricted_user_id
      FROM restriction_appeals a
      JOIN user_restrictions r ON a.restriction_id = r.id
      WHERE a.id = ?
    `, [appealId]);

    if (!Array.isArray(currentAppeal) || currentAppeal.length === 0) {
      return NextResponse.json(
        { error: 'Appeal not found' },
        { status: 404 }
      );
    }

    const appeal = currentAppeal[0] as any;

    let updateQuery = '';
    let updateParams = [];
    let actionType = '';

    switch (action) {
      case 'assign':
        updateQuery = `
          UPDATE restriction_appeals 
          SET assigned_admin_id = ?, status = 'under_review', updated_at = NOW()
          WHERE id = ?
        `;
        updateParams = [adminId, appealId];
        actionType = 'appeal_reviewed';
        break;

      case 'approve':
        updateQuery = `
          UPDATE restriction_appeals 
          SET status = 'approved', admin_response = ?, reviewed_at = NOW(), 
              decision_at = NOW(), assigned_admin_id = ?, updated_at = NOW()
          WHERE id = ?
        `;
        updateParams = [response, adminId, appealId];
        actionType = 'appeal_approved';

        // Also lift the restriction
        await db.execute(`
          UPDATE user_restrictions 
          SET status = 'lifted', lifted_at = NOW(), lifted_by_admin_id = ?, 
              lift_reason = 'Appeal approved', updated_at = NOW()
          WHERE id = ?
        `, [adminId, appeal.restriction_id]);

        // Update user's restriction status
        const [otherRestrictions] = await db.execute(`
          SELECT COUNT(*) as count
          FROM user_restrictions
          WHERE user_id = ? AND status = 'active' AND id != ?
        `, [appeal.restricted_user_id, appeal.restriction_id]);

        const hasOtherRestrictions = (otherRestrictions as any)[0].count > 0;

        await db.execute(`
          UPDATE users 
          SET restriction_status = ?, last_restriction_check = NOW()
          WHERE user_id = ?
        `, [hasOtherRestrictions ? 'restricted' : 'none', appeal.restricted_user_id]);

        break;

      case 'deny':
        updateQuery = `
          UPDATE restriction_appeals 
          SET status = 'denied', admin_response = ?, reviewed_at = NOW(), 
              decision_at = NOW(), assigned_admin_id = ?, updated_at = NOW()
          WHERE id = ?
        `;
        updateParams = [response, adminId, appealId];
        actionType = 'appeal_denied';
        break;

      case 'escalate':
        updateQuery = `
          UPDATE restriction_appeals 
          SET status = 'escalated', escalation_reason = ?, 
              assigned_admin_id = NULL, updated_at = NOW()
          WHERE id = ?
        `;
        updateParams = [escalationReason, appealId];
        actionType = 'appeal_escalated';
        break;

      case 'update_notes':
        updateQuery = `
          UPDATE restriction_appeals 
          SET internal_notes = ?, updated_at = NOW()
          WHERE id = ?
        `;
        updateParams = [internalNotes, appealId];
        actionType = 'appeal_reviewed';
        break;

      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        );
    }

    // Execute the update
    await db.execute(updateQuery, updateParams);

    // Add communication record if there's a response
    if (response && ['approve', 'deny'].includes(action)) {
      await db.execute(`
        INSERT INTO appeal_communications (
          appeal_id, sender_id, sender_type, message, is_internal, created_at
        ) VALUES (?, ?, 'admin', ?, FALSE, NOW())
      `, [appealId, adminId, response]);
    }

    // Log the action
    try {
      await logAdminAction({
        adminId,
        actionType,
        targetType: 'restriction_appeal',
        targetId: appealId,
        affectedUserId: appeal.user_id,
        actionDetails: {
          action,
          response,
          internalNotes,
          escalationReason
        },
        reason: `Appeal ${action} by admin`
      });
    } catch (logError) {
      console.error('Failed to log action:', logError);
    }

    // Send notifications
    try {
      if (action === 'escalate') {
        await sendAdminNotification({
          type: 'appeal_escalated',
          title: 'Appeal Escalated',
          message: `Appeal #${appealId} has been escalated for senior review`,
          data: {
            appealId,
            escalationReason,
            escalatedBy: adminId
          },
          priority: 'high'
        });
      }
    } catch (notificationError) {
      console.error('Failed to send notification:', notificationError);
    }

    return NextResponse.json({
      success: true,
      message: `Appeal ${action} completed successfully`
    });

  } catch (error) {
    console.error('Error updating appeal:', error);
    return NextResponse.json(
      { error: 'Failed to update appeal' },
      { status: 500 }
    );
  }
}

// Add communication to appeal
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id || session.user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    const appealId = parseInt(params.id);
    const adminId = parseInt(session.user.id);
    const { message, isInternal = false } = await request.json();

    if (!message || message.trim().length === 0) {
      return NextResponse.json(
        { error: 'Message is required' },
        { status: 400 }
      );
    }

    // Verify appeal exists
    const [appeal] = await db.execute(`
      SELECT id FROM restriction_appeals WHERE id = ?
    `, [appealId]);

    if (!Array.isArray(appeal) || appeal.length === 0) {
      return NextResponse.json(
        { error: 'Appeal not found' },
        { status: 404 }
      );
    }

    // Add communication
    await db.execute(`
      INSERT INTO appeal_communications (
        appeal_id, sender_id, sender_type, message, is_internal, created_at
      ) VALUES (?, ?, 'admin', ?, ?, NOW())
    `, [appealId, adminId, message.trim(), isInternal]);

    return NextResponse.json({
      success: true,
      message: 'Communication added successfully'
    });

  } catch (error) {
    console.error('Error adding communication:', error);
    return NextResponse.json(
      { error: 'Failed to add communication' },
      { status: 500 }
    );
  }
}
