import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { db } from '@/lib/db';
import { logAdminAction } from '@/services/auditService';
import { sendAdminNotification } from '@/utils/notificationService';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id || session.user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    const reportId = parseInt(params.id);

    // Get detailed report information
    const [reports] = await db.execute(`
      SELECT 
        r.*,
        reporter.first_name as reporter_first_name,
        reporter.last_name as reporter_last_name,
        reporter.email as reporter_email,
        reporter.role as reporter_role,
        reported.first_name as reported_first_name,
        reported.last_name as reported_last_name,
        reported.email as reported_email,
        reported.role as reported_role,
        admin.first_name as admin_first_name,
        admin.last_name as admin_last_name,
        booking.service_name,
        booking.booking_date,
        booking.booking_time,
        booking.status as booking_status
      FROM user_reports r
      JOIN users reporter ON r.reporter_id = reporter.user_id
      JOIN users reported ON r.reported_user_id = reported.user_id
      LEFT JOIN users admin ON r.assigned_admin_id = admin.user_id
      LEFT JOIN service_bookings booking ON r.booking_id = booking.id
      WHERE r.id = ?
    `, [reportId]);

    if (!Array.isArray(reports) || reports.length === 0) {
      return NextResponse.json(
        { error: 'Report not found' },
        { status: 404 }
      );
    }

    const report = reports[0] as any;

    // Get user history (previous reports involving the reported user)
    const [userHistory] = await db.execute(`
      SELECT 
        r.id,
        r.category,
        r.status,
        r.created_at,
        reporter.first_name as reporter_first_name,
        reporter.last_name as reporter_last_name
      FROM user_reports r
      JOIN users reporter ON r.reporter_id = reporter.user_id
      WHERE r.reported_user_id = ? AND r.id != ?
      ORDER BY r.created_at DESC
      LIMIT 10
    `, [report.reported_user_id, reportId]);

    // Get any existing restrictions for the reported user
    const [restrictions] = await db.execute(`
      SELECT 
        id,
        restriction_type,
        status,
        start_date,
        end_date,
        is_permanent,
        severity,
        custom_reason
      FROM user_restrictions
      WHERE user_id = ?
      ORDER BY created_at DESC
      LIMIT 5
    `, [report.reported_user_id]);

    // Format the response
    const formattedReport = {
      id: report.id,
      category: report.category,
      description: report.description,
      status: report.status,
      priority: report.priority,
      createdAt: report.created_at,
      updatedAt: report.updated_at,
      resolvedAt: report.resolved_at,
      evidenceFiles: report.evidence_files ? JSON.parse(report.evidence_files) : null,
      adminNotes: report.admin_notes,
      resolutionNotes: report.resolution_notes,
      reporter: {
        id: report.reporter_id,
        name: `${report.reporter_first_name} ${report.reporter_last_name}`,
        email: report.reporter_email,
        role: report.reporter_role
      },
      reportedUser: {
        id: report.reported_user_id,
        name: `${report.reported_first_name} ${report.reported_last_name}`,
        email: report.reported_email,
        role: report.reported_role
      },
      assignedAdmin: report.admin_first_name ? {
        id: report.assigned_admin_id,
        name: `${report.admin_first_name} ${report.admin_last_name}`
      } : null,
      booking: report.booking_id ? {
        id: report.booking_id,
        serviceName: report.service_name,
        date: report.booking_date,
        time: report.booking_time,
        status: report.booking_status
      } : null,
      userHistory: (userHistory as any[]).map(h => ({
        id: h.id,
        category: h.category,
        status: h.status,
        createdAt: h.created_at,
        reporterName: `${h.reporter_first_name} ${h.reporter_last_name}`
      })),
      userRestrictions: restrictions as any[]
    };

    return NextResponse.json({ report: formattedReport });

  } catch (error) {
    console.error('Error fetching report details:', error);
    return NextResponse.json(
      { error: 'Failed to fetch report details' },
      { status: 500 }
    );
  }
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id || session.user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    const reportId = parseInt(params.id);
    const adminId = parseInt(session.user.id);
    const updates = await request.json();

    // Get current report data for logging
    const [currentReport] = await db.execute(`
      SELECT * FROM user_reports WHERE id = ?
    `, [reportId]);

    if (!Array.isArray(currentReport) || currentReport.length === 0) {
      return NextResponse.json(
        { error: 'Report not found' },
        { status: 404 }
      );
    }

    const previousState = currentReport[0];

    // Build update query
    const allowedFields = ['status', 'priority', 'assigned_admin_id', 'admin_notes', 'resolution_notes'];
    const updateFields = [];
    const updateValues = [];

    for (const [field, value] of Object.entries(updates)) {
      if (allowedFields.includes(field) && value !== undefined) {
        updateFields.push(`${field} = ?`);
        updateValues.push(value);
      }
    }

    if (updateFields.length === 0) {
      return NextResponse.json(
        { error: 'No valid fields to update' },
        { status: 400 }
      );
    }

    // Add resolved_at if status is being set to resolved
    if (updates.status === 'resolved') {
      updateFields.push('resolved_at = NOW()');
    }

    // Add updated_at
    updateFields.push('updated_at = NOW()');

    // Execute update
    await db.execute(`
      UPDATE user_reports 
      SET ${updateFields.join(', ')}
      WHERE id = ?
    `, [...updateValues, reportId]);

    // Log the action
    try {
      await logAdminAction({
        adminId,
        actionType: 'report_reviewed',
        targetType: 'user_report',
        targetId: reportId,
        affectedUserId: (previousState as any).reported_user_id,
        actionDetails: updates,
        previousState,
        newState: { ...previousState, ...updates },
        reason: `Report ${reportId} updated by admin`
      });
    } catch (logError) {
      console.error('Failed to log action:', logError);
    }

    // Send notifications if status changed
    if (updates.status && updates.status !== (previousState as any).status) {
      try {
        // Notify other admins of status change
        await sendAdminNotification({
          type: 'report_status_changed',
          title: 'Report Status Updated',
          message: `Report #${reportId} status changed to ${updates.status}`,
          data: {
            reportId,
            oldStatus: (previousState as any).status,
            newStatus: updates.status,
            updatedBy: adminId
          },
          priority: 'low'
        });
      } catch (notificationError) {
        console.error('Failed to send notification:', notificationError);
      }
    }

    return NextResponse.json({
      success: true,
      message: 'Report updated successfully'
    });

  } catch (error) {
    console.error('Error updating report:', error);
    return NextResponse.json(
      { error: 'Failed to update report' },
      { status: 500 }
    );
  }
}

// Apply restriction based on report
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id || session.user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    const reportId = parseInt(params.id);
    const adminId = parseInt(session.user.id);
    const { restrictionType, reasonCategory, customReason, duration, isPermanent } = await request.json();

    // Get report details
    const [reports] = await db.execute(`
      SELECT reported_user_id, category FROM user_reports WHERE id = ?
    `, [reportId]);

    if (!Array.isArray(reports) || reports.length === 0) {
      return NextResponse.json(
        { error: 'Report not found' },
        { status: 404 }
      );
    }

    const report = reports[0] as any;

    // Calculate end date
    let endDate = null;
    if (!isPermanent && duration) {
      endDate = new Date();
      endDate.setDate(endDate.getDate() + duration);
    }

    // Create restriction
    const [result] = await db.execute(`
      INSERT INTO user_restrictions (
        user_id, restriction_type, reason_category, custom_reason,
        start_date, end_date, is_permanent, applied_by_admin_id,
        status, related_report_id, user_notified, created_at
      ) VALUES (?, ?, ?, ?, NOW(), ?, ?, ?, 'active', ?, FALSE, NOW())
    `, [
      report.reported_user_id,
      restrictionType,
      reasonCategory,
      customReason,
      endDate,
      isPermanent,
      adminId,
      reportId
    ]);

    const restrictionId = (result as any).insertId;

    // Update report status to resolved
    await db.execute(`
      UPDATE user_reports 
      SET status = 'resolved', resolved_at = NOW(), updated_at = NOW()
      WHERE id = ?
    `, [reportId]);

    // Log the action
    try {
      await logAdminAction({
        adminId,
        actionType: 'restriction_applied',
        targetType: 'user_restriction',
        targetId: restrictionId,
        affectedUserId: report.reported_user_id,
        actionDetails: {
          restrictionType,
          reasonCategory,
          customReason,
          duration,
          isPermanent,
          relatedReportId: reportId
        },
        reason: `Restriction applied based on report ${reportId}`
      });
    } catch (logError) {
      console.error('Failed to log action:', logError);
    }

    return NextResponse.json({
      success: true,
      message: 'Restriction applied successfully',
      restrictionId
    });

  } catch (error) {
    console.error('Error applying restriction:', error);
    return NextResponse.json(
      { error: 'Failed to apply restriction' },
      { status: 500 }
    );
  }
}
