import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { db } from '@/lib/db';

export async function GET(request: NextRequest) {
  try {
    // Check authentication and admin role
    const session = await getServerSession(authOptions);
    if (!session?.user?.id || session.user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    
    // Parse query parameters
    const page = parseInt(searchParams.get('page') || '1');
    const limit = Math.min(parseInt(searchParams.get('limit') || '20'), 100);
    const status = searchParams.get('status');
    const category = searchParams.get('category');
    const priority = searchParams.get('priority');
    const search = searchParams.get('search');
    
    const offset = (page - 1) * limit;

    // Build query
    let baseQuery = `
      SELECT 
        r.id,
        r.category,
        r.description,
        r.status,
        r.priority,
        r.created_at,
        r.updated_at,
        r.resolved_at,
        r.booking_id,
        r.evidence_files,
        r.admin_notes,
        r.resolution_notes,
        r.assigned_admin_id,
        reporter.first_name as reporter_first_name,
        reporter.last_name as reporter_last_name,
        reported.first_name as reported_first_name,
        reported.last_name as reported_last_name,
        r.reporter_id,
        r.reported_user_id,
        admin.first_name as admin_first_name,
        admin.last_name as admin_last_name
      FROM user_reports r
      JOIN users reporter ON r.reporter_id = reporter.user_id
      JOIN users reported ON r.reported_user_id = reported.user_id
      LEFT JOIN users admin ON r.assigned_admin_id = admin.user_id
    `;

    let whereConditions = [];
    let queryParams = [];

    // Filter by status
    if (status && ['pending', 'under_review', 'resolved', 'dismissed'].includes(status)) {
      whereConditions.push('r.status = ?');
      queryParams.push(status);
    }

    // Filter by category
    if (category) {
      whereConditions.push('r.category = ?');
      queryParams.push(category);
    }

    // Filter by priority
    if (priority && ['low', 'medium', 'high', 'urgent'].includes(priority)) {
      whereConditions.push('r.priority = ?');
      queryParams.push(priority);
    }

    // Search functionality
    if (search) {
      whereConditions.push(`(
        r.description LIKE ? OR 
        reporter.first_name LIKE ? OR 
        reporter.last_name LIKE ? OR
        reported.first_name LIKE ? OR 
        reported.last_name LIKE ?
      )`);
      const searchTerm = `%${search}%`;
      queryParams.push(searchTerm, searchTerm, searchTerm, searchTerm, searchTerm);
    }

    // Combine conditions
    if (whereConditions.length > 0) {
      baseQuery += ' WHERE ' + whereConditions.join(' AND ');
    }

    // Add ordering and pagination
    baseQuery += ' ORDER BY r.created_at DESC LIMIT ? OFFSET ?';
    queryParams.push(limit, offset);

    // Execute query
    const [reports] = await db.execute(baseQuery, queryParams);

    // Get total count for pagination
    let countQuery = `
      SELECT COUNT(*) as total
      FROM user_reports r
      JOIN users reporter ON r.reporter_id = reporter.user_id
      JOIN users reported ON r.reported_user_id = reported.user_id
    `;

    let countParams = [];
    if (whereConditions.length > 0) {
      countQuery += ' WHERE ' + whereConditions.join(' AND ');
      // Remove limit and offset from count params
      countParams = queryParams.slice(0, -2);
    }

    const [countResult] = await db.execute(countQuery, countParams);
    const total = (countResult as any)[0].total;

    // Format response
    const formattedReports = (reports as any[]).map(report => ({
      id: report.id,
      category: report.category,
      description: report.description,
      status: report.status,
      priority: report.priority,
      createdAt: report.created_at,
      updatedAt: report.updated_at,
      resolvedAt: report.resolved_at,
      bookingId: report.booking_id,
      evidenceFiles: report.evidence_files ? JSON.parse(report.evidence_files) : null,
      adminNotes: report.admin_notes,
      resolutionNotes: report.resolution_notes,
      reporter: {
        id: report.reporter_id,
        name: `${report.reporter_first_name} ${report.reporter_last_name}`
      },
      reportedUser: {
        id: report.reported_user_id,
        name: `${report.reported_first_name} ${report.reported_last_name}`
      },
      assignedAdmin: report.admin_first_name ? {
        id: report.assigned_admin_id,
        name: `${report.admin_first_name} ${report.admin_last_name}`
      } : null
    }));

    return NextResponse.json({
      reports: formattedReports,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNext: page * limit < total,
        hasPrev: page > 1
      }
    });

  } catch (error) {
    console.error('Error fetching admin reports:', error);
    return NextResponse.json(
      { error: 'Failed to fetch reports' },
      { status: 500 }
    );
  }
}

// Bulk actions on reports
export async function PATCH(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id || session.user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    const { reportIds, action, data } = await request.json();
    const adminId = parseInt(session.user.id);

    if (!reportIds || !Array.isArray(reportIds) || reportIds.length === 0) {
      return NextResponse.json(
        { error: 'Report IDs are required' },
        { status: 400 }
      );
    }

    if (!action) {
      return NextResponse.json(
        { error: 'Action is required' },
        { status: 400 }
      );
    }

    const placeholders = reportIds.map(() => '?').join(',');
    let updateQuery = '';
    let updateParams = [];

    switch (action) {
      case 'assign':
        if (!data?.adminId) {
          return NextResponse.json(
            { error: 'Admin ID is required for assignment' },
            { status: 400 }
          );
        }
        updateQuery = `
          UPDATE user_reports 
          SET assigned_admin_id = ?, updated_at = NOW()
          WHERE id IN (${placeholders})
        `;
        updateParams = [data.adminId, ...reportIds];
        break;

      case 'update_status':
        if (!data?.status || !['pending', 'under_review', 'resolved', 'dismissed'].includes(data.status)) {
          return NextResponse.json(
            { error: 'Valid status is required' },
            { status: 400 }
          );
        }
        updateQuery = `
          UPDATE user_reports 
          SET status = ?, updated_at = NOW()
          ${data.status === 'resolved' ? ', resolved_at = NOW()' : ''}
          WHERE id IN (${placeholders})
        `;
        updateParams = [data.status, ...reportIds];
        break;

      case 'update_priority':
        if (!data?.priority || !['low', 'medium', 'high', 'urgent'].includes(data.priority)) {
          return NextResponse.json(
            { error: 'Valid priority is required' },
            { status: 400 }
          );
        }
        updateQuery = `
          UPDATE user_reports 
          SET priority = ?, updated_at = NOW()
          WHERE id IN (${placeholders})
        `;
        updateParams = [data.priority, ...reportIds];
        break;

      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        );
    }

    // Execute the update
    await db.execute(updateQuery, updateParams);

    // Log the bulk action
    try {
      const { logAdminAction } = await import('@/services/auditService');
      await logAdminAction({
        adminId,
        actionType: 'bulk_action',
        targetType: 'user_report',
        actionDetails: {
          action,
          reportIds,
          data,
          affectedCount: reportIds.length
        },
        reason: `Bulk ${action} on ${reportIds.length} reports`
      });
    } catch (logError) {
      console.error('Failed to log bulk action:', logError);
    }

    return NextResponse.json({
      success: true,
      message: `Successfully performed ${action} on ${reportIds.length} reports`,
      affectedCount: reportIds.length
    });

  } catch (error) {
    console.error('Error performing bulk action:', error);
    return NextResponse.json(
      { error: 'Failed to perform bulk action' },
      { status: 500 }
    );
  }
}
