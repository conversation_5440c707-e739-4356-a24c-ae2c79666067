import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { db } from '@/lib/db';
import { logAdminAction } from '@/services/auditService';
import { sendAdminNotification } from '@/utils/notificationService';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id || session.user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    const restrictionId = parseInt(params.id);

    // Get detailed restriction information
    const [restrictions] = await db.execute(`
      SELECT 
        r.*,
        u.first_name,
        u.last_name,
        u.email,
        u.role,
        u.created_at as user_created_at,
        admin.first_name as admin_first_name,
        admin.last_name as admin_last_name,
        report.id as report_id,
        report.category as report_category,
        report.description as report_description
      FROM user_restrictions r
      JOIN users u ON r.user_id = u.user_id
      LEFT JOIN users admin ON r.applied_by_admin_id = admin.user_id
      LEFT JOIN user_reports report ON r.related_report_id = report.id
      WHERE r.id = ?
    `, [restrictionId]);

    if (!Array.isArray(restrictions) || restrictions.length === 0) {
      return NextResponse.json(
        { error: 'Restriction not found' },
        { status: 404 }
      );
    }

    const restriction = restrictions[0] as any;

    // Get user's restriction history
    const [restrictionHistory] = await db.execute(`
      SELECT 
        id,
        restriction_type,
        reason_category,
        status,
        severity,
        start_date,
        end_date,
        is_permanent,
        created_at
      FROM user_restrictions
      WHERE user_id = ? AND id != ?
      ORDER BY created_at DESC
      LIMIT 10
    `, [restriction.user_id, restrictionId]);

    // Get any pending appeals for this restriction
    const [appeals] = await db.execute(`
      SELECT 
        id,
        appeal_reason,
        status,
        submitted_at,
        admin_response
      FROM restriction_appeals
      WHERE restriction_id = ?
      ORDER BY submitted_at DESC
    `, [restrictionId]);

    // Format the response
    const formattedRestriction = {
      id: restriction.id,
      user: {
        id: restriction.user_id,
        name: `${restriction.first_name} ${restriction.last_name}`,
        email: restriction.email,
        role: restriction.role,
        memberSince: restriction.user_created_at
      },
      restrictionType: restriction.restriction_type,
      reasonCategory: restriction.reason_category,
      customReason: restriction.custom_reason,
      status: restriction.status,
      severity: restriction.severity,
      startDate: restriction.start_date,
      endDate: restriction.end_date,
      isPermanent: Boolean(restriction.is_permanent),
      adminNotes: restriction.admin_notes,
      userNotified: Boolean(restriction.user_notified),
      appealDeadline: restriction.appeal_deadline,
      appliedBy: restriction.admin_first_name ? {
        name: `${restriction.admin_first_name} ${restriction.admin_last_name}`
      } : null,
      relatedReport: restriction.report_id ? {
        id: restriction.report_id,
        category: restriction.report_category,
        description: restriction.report_description
      } : null,
      createdAt: restriction.created_at,
      updatedAt: restriction.updated_at,
      liftedAt: restriction.lifted_at,
      liftedBy: restriction.lifted_by_admin_id,
      liftReason: restriction.lift_reason,
      restrictionHistory: restrictionHistory as any[],
      appeals: appeals as any[]
    };

    return NextResponse.json({ restriction: formattedRestriction });

  } catch (error) {
    console.error('Error fetching restriction details:', error);
    return NextResponse.json(
      { error: 'Failed to fetch restriction details' },
      { status: 500 }
    );
  }
}

// Modify restriction
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id || session.user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    const restrictionId = parseInt(params.id);
    const adminId = parseInt(session.user.id);
    const updates = await request.json();

    // Get current restriction data
    const [currentRestriction] = await db.execute(`
      SELECT * FROM user_restrictions WHERE id = ?
    `, [restrictionId]);

    if (!Array.isArray(currentRestriction) || currentRestriction.length === 0) {
      return NextResponse.json(
        { error: 'Restriction not found' },
        { status: 404 }
      );
    }

    const previousState = currentRestriction[0] as any;

    // Build update query
    const allowedFields = ['status', 'severity', 'end_date', 'is_permanent', 'admin_notes', 'user_notified'];
    const updateFields = [];
    const updateValues = [];

    for (const [field, value] of Object.entries(updates)) {
      if (allowedFields.includes(field) && value !== undefined) {
        updateFields.push(`${field} = ?`);
        updateValues.push(value);
      }
    }

    if (updateFields.length === 0) {
      return NextResponse.json(
        { error: 'No valid fields to update' },
        { status: 400 }
      );
    }

    // Add updated_at
    updateFields.push('updated_at = NOW()');

    // If lifting restriction, add lifted_at and lifted_by
    if (updates.status === 'lifted') {
      updateFields.push('lifted_at = NOW()');
      updateFields.push('lifted_by_admin_id = ?');
      updateValues.push(adminId);
    }

    // Execute update
    await db.execute(`
      UPDATE user_restrictions 
      SET ${updateFields.join(', ')}
      WHERE id = ?
    `, [...updateValues, restrictionId]);

    // Update user's overall restriction status if needed
    if (updates.status === 'lifted' || updates.status === 'expired') {
      // Check if user has any other active restrictions
      const [otherRestrictions] = await db.execute(`
        SELECT COUNT(*) as count
        FROM user_restrictions
        WHERE user_id = ? AND status = 'active' AND id != ?
      `, [previousState.user_id, restrictionId]);

      const hasOtherRestrictions = (otherRestrictions as any)[0].count > 0;

      await db.execute(`
        UPDATE users 
        SET restriction_status = ?, last_restriction_check = NOW()
        WHERE user_id = ?
      `, [hasOtherRestrictions ? 'restricted' : 'none', previousState.user_id]);
    }

    // Log the action
    try {
      await logAdminAction({
        adminId,
        actionType: updates.status === 'lifted' ? 'restriction_lifted' : 'restriction_modified',
        targetType: 'user_restriction',
        targetId: restrictionId,
        affectedUserId: previousState.user_id,
        actionDetails: updates,
        previousState,
        newState: { ...previousState, ...updates },
        reason: updates.status === 'lifted' ? 
          `Restriction lifted: ${updates.lift_reason || 'No reason provided'}` :
          'Restriction modified by admin'
      });
    } catch (logError) {
      console.error('Failed to log action:', logError);
    }

    // Send notifications
    try {
      if (updates.status === 'lifted') {
        await sendAdminNotification({
          type: 'restriction_lifted',
          title: 'Restriction Lifted',
          message: `${previousState.restriction_type.replace('_', ' ')} restriction has been lifted`,
          data: {
            restrictionId,
            userId: previousState.user_id,
            restrictionType: previousState.restriction_type,
            liftedBy: adminId
          },
          priority: 'low'
        });
      }
    } catch (notificationError) {
      console.error('Failed to send notification:', notificationError);
    }

    return NextResponse.json({
      success: true,
      message: updates.status === 'lifted' ? 'Restriction lifted successfully' : 'Restriction updated successfully'
    });

  } catch (error) {
    console.error('Error updating restriction:', error);
    return NextResponse.json(
      { error: 'Failed to update restriction' },
      { status: 500 }
    );
  }
}

// Delete restriction (admin override)
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id || session.user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    const restrictionId = parseInt(params.id);
    const adminId = parseInt(session.user.id);

    // Get restriction data before deletion
    const [restriction] = await db.execute(`
      SELECT * FROM user_restrictions WHERE id = ?
    `, [restrictionId]);

    if (!Array.isArray(restriction) || restriction.length === 0) {
      return NextResponse.json(
        { error: 'Restriction not found' },
        { status: 404 }
      );
    }

    const restrictionData = restriction[0] as any;

    // Delete the restriction
    await db.execute(`
      DELETE FROM user_restrictions WHERE id = ?
    `, [restrictionId]);

    // Update user's restriction status
    const [otherRestrictions] = await db.execute(`
      SELECT COUNT(*) as count
      FROM user_restrictions
      WHERE user_id = ? AND status = 'active'
    `, [restrictionData.user_id]);

    const hasOtherRestrictions = (otherRestrictions as any)[0].count > 0;

    await db.execute(`
      UPDATE users 
      SET restriction_status = ?, last_restriction_check = NOW()
      WHERE user_id = ?
    `, [hasOtherRestrictions ? 'restricted' : 'none', restrictionData.user_id]);

    // Log the action
    try {
      await logAdminAction({
        adminId,
        actionType: 'system_override',
        targetType: 'user_restriction',
        targetId: restrictionId,
        affectedUserId: restrictionData.user_id,
        actionDetails: {
          action: 'delete_restriction',
          deletedRestriction: restrictionData
        },
        reason: 'Restriction deleted by admin override',
        severity: 'high'
      });
    } catch (logError) {
      console.error('Failed to log deletion:', logError);
    }

    return NextResponse.json({
      success: true,
      message: 'Restriction deleted successfully'
    });

  } catch (error) {
    console.error('Error deleting restriction:', error);
    return NextResponse.json(
      { error: 'Failed to delete restriction' },
      { status: 500 }
    );
  }
}
