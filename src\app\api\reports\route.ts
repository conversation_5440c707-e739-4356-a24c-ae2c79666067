import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { db } from '@/lib/db';

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const userId = parseInt(session.user.id);
    const { searchParams } = new URL(request.url);
    
    // Parse query parameters
    const page = parseInt(searchParams.get('page') || '1');
    const limit = Math.min(parseInt(searchParams.get('limit') || '10'), 50);
    const status = searchParams.get('status');
    const type = searchParams.get('type'); // 'submitted' or 'received'
    
    const offset = (page - 1) * limit;

    // Build query based on user role and type
    let baseQuery = `
      SELECT 
        r.id,
        r.category,
        r.description,
        r.status,
        r.priority,
        r.created_at,
        r.updated_at,
        r.resolved_at,
        r.booking_id,
        reporter.first_name as reporter_first_name,
        reporter.last_name as reporter_last_name,
        reported.first_name as reported_first_name,
        reported.last_name as reported_last_name,
        r.reporter_id,
        r.reported_user_id
      FROM user_reports r
      JOIN users reporter ON r.reporter_id = reporter.user_id
      JOIN users reported ON r.reported_user_id = reported.user_id
    `;

    let whereConditions = [];
    let queryParams = [];

    // Filter by user involvement
    if (type === 'submitted') {
      whereConditions.push('r.reporter_id = ?');
      queryParams.push(userId);
    } else if (type === 'received') {
      whereConditions.push('r.reported_user_id = ?');
      queryParams.push(userId);
    } else {
      // Show both submitted and received reports
      whereConditions.push('(r.reporter_id = ? OR r.reported_user_id = ?)');
      queryParams.push(userId, userId);
    }

    // Filter by status if provided
    if (status && ['pending', 'under_review', 'resolved', 'dismissed'].includes(status)) {
      whereConditions.push('r.status = ?');
      queryParams.push(status);
    }

    // Combine conditions
    if (whereConditions.length > 0) {
      baseQuery += ' WHERE ' + whereConditions.join(' AND ');
    }

    // Add ordering and pagination
    baseQuery += ' ORDER BY r.created_at DESC LIMIT ? OFFSET ?';
    queryParams.push(limit, offset);

    // Execute query
    const [reports] = await db.execute(baseQuery, queryParams);

    // Get total count for pagination
    let countQuery = `
      SELECT COUNT(*) as total
      FROM user_reports r
    `;

    let countParams = [];
    if (whereConditions.length > 0) {
      countQuery += ' WHERE ' + whereConditions.join(' AND ');
      // Remove limit and offset from count params
      countParams = queryParams.slice(0, -2);
    }

    const [countResult] = await db.execute(countQuery, countParams);
    const total = (countResult as any)[0].total;

    // Format response
    const formattedReports = (reports as any[]).map(report => ({
      id: report.id,
      category: report.category,
      description: report.description,
      status: report.status,
      priority: report.priority,
      createdAt: report.created_at,
      updatedAt: report.updated_at,
      resolvedAt: report.resolved_at,
      bookingId: report.booking_id,
      reporter: {
        id: report.reporter_id,
        name: `${report.reporter_first_name} ${report.reporter_last_name}`,
        isCurrentUser: report.reporter_id === userId
      },
      reportedUser: {
        id: report.reported_user_id,
        name: `${report.reported_first_name} ${report.reported_last_name}`,
        isCurrentUser: report.reported_user_id === userId
      }
    }));

    return NextResponse.json({
      reports: formattedReports,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNext: page * limit < total,
        hasPrev: page > 1
      }
    });

  } catch (error) {
    console.error('Error fetching reports:', error);
    return NextResponse.json(
      { error: 'Failed to fetch reports' },
      { status: 500 }
    );
  }
}

// Get report details by ID
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { reportId, action } = await request.json();
    const userId = parseInt(session.user.id);

    if (!reportId || !action) {
      return NextResponse.json(
        { error: 'Report ID and action are required' },
        { status: 400 }
      );
    }

    // Verify user is involved in the report
    const [report] = await db.execute(`
      SELECT reporter_id, reported_user_id, status
      FROM user_reports
      WHERE id = ?
    `, [reportId]);

    if (!Array.isArray(report) || report.length === 0) {
      return NextResponse.json(
        { error: 'Report not found' },
        { status: 404 }
      );
    }

    const reportData = report[0] as any;
    const isInvolved = reportData.reporter_id === userId || reportData.reported_user_id === userId;

    if (!isInvolved) {
      return NextResponse.json(
        { error: 'You are not authorized to perform this action' },
        { status: 403 }
      );
    }

    // Handle different actions
    switch (action) {
      case 'withdraw':
        // Only reporter can withdraw
        if (reportData.reporter_id !== userId) {
          return NextResponse.json(
            { error: 'Only the reporter can withdraw a report' },
            { status: 403 }
          );
        }

        if (reportData.status !== 'pending') {
          return NextResponse.json(
            { error: 'Only pending reports can be withdrawn' },
            { status: 400 }
          );
        }

        await db.execute(`
          UPDATE user_reports 
          SET status = 'dismissed', updated_at = NOW()
          WHERE id = ?
        `, [reportId]);

        return NextResponse.json({
          success: true,
          message: 'Report withdrawn successfully'
        });

      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('Error updating report:', error);
    return NextResponse.json(
      { error: 'Failed to update report' },
      { status: 500 }
    );
  }
}
