import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { db } from '@/lib/db';

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const userId = parseInt(session.user.id);

    // Get user's restrictions
    const [restrictions] = await db.execute(`
      SELECT 
        r.*,
        admin.first_name as admin_first_name,
        admin.last_name as admin_last_name
      FROM user_restrictions r
      LEFT JOIN users admin ON r.applied_by_admin_id = admin.user_id
      WHERE r.user_id = ?
      ORDER BY r.created_at DESC
    `, [userId]);

    // Format the restrictions data
    const formattedRestrictions = (restrictions as any[]).map(restriction => ({
      id: restriction.id,
      restriction_type: restriction.restriction_type,
      reason_category: restriction.reason_category,
      custom_reason: restriction.custom_reason,
      start_date: restriction.start_date,
      end_date: restriction.end_date,
      is_permanent: Boolean(restriction.is_permanent),
      status: restriction.status,
      severity: restriction.severity,
      admin_notes: restriction.admin_notes,
      user_notified: Boolean(restriction.user_notified),
      appeal_deadline: restriction.appeal_deadline,
      created_at: restriction.created_at,
      updated_at: restriction.updated_at,
      applied_by_admin: restriction.admin_first_name ? 
        `${restriction.admin_first_name} ${restriction.admin_last_name}` : 
        'System'
    }));

    // Check if user has any active restrictions
    const hasActiveRestrictions = formattedRestrictions.some(r => r.status === 'active');

    // Update user's restriction status if needed
    if (hasActiveRestrictions) {
      await db.execute(`
        UPDATE users 
        SET restriction_status = 'restricted', last_restriction_check = NOW()
        WHERE user_id = ?
      `, [userId]);
    } else {
      await db.execute(`
        UPDATE users 
        SET restriction_status = 'none', last_restriction_check = NOW()
        WHERE user_id = ?
      `, [userId]);
    }

    return NextResponse.json({
      restrictions: formattedRestrictions,
      hasActiveRestrictions,
      totalRestrictions: formattedRestrictions.length
    });

  } catch (error) {
    console.error('Error fetching user restrictions:', error);
    return NextResponse.json(
      { error: 'Failed to fetch restrictions' },
      { status: 500 }
    );
  }
}

// Check if user has specific restriction type
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { restrictionType } = await request.json();
    const userId = parseInt(session.user.id);

    if (!restrictionType) {
      return NextResponse.json(
        { error: 'Restriction type is required' },
        { status: 400 }
      );
    }

    // Check for active restriction of specific type
    const [restrictions] = await db.execute(`
      SELECT id, restriction_type, end_date, is_permanent, status
      FROM user_restrictions
      WHERE user_id = ? AND restriction_type = ? AND status = 'active'
      AND (end_date IS NULL OR end_date > NOW() OR is_permanent = TRUE)
    `, [userId, restrictionType]);

    const hasRestriction = Array.isArray(restrictions) && restrictions.length > 0;
    const restrictionData = hasRestriction ? restrictions[0] : null;

    return NextResponse.json({
      hasRestriction,
      restriction: restrictionData,
      message: hasRestriction ? 
        `You currently have an active ${restrictionType.replace('_', ' ')} restriction` :
        `No active ${restrictionType.replace('_', ' ')} restriction found`
    });

  } catch (error) {
    console.error('Error checking user restriction:', error);
    return NextResponse.json(
      { error: 'Failed to check restriction' },
      { status: 500 }
    );
  }
}
