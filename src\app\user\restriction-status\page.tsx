'use client';

import React, { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { 
  AlertTriangle, 
  Clock, 
  FileText, 
  MessageSquare, 
  Calendar,
  CheckCircle,
  XCircle,
  Info,
  ArrowLeft
} from 'lucide-react';
import { toast } from 'react-hot-toast';

interface Restriction {
  id: number;
  restriction_type: string;
  reason_category: string;
  custom_reason: string;
  start_date: string;
  end_date: string | null;
  is_permanent: boolean;
  status: string;
  severity: string;
  admin_notes: string;
  appeal_deadline: string | null;
  created_at: string;
}

interface Appeal {
  id: number;
  restriction_id: number;
  appeal_reason: string;
  status: string;
  submitted_at: string;
  admin_response: string | null;
}

export default function RestrictionStatusPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [restrictions, setRestrictions] = useState<Restriction[]>([]);
  const [appeals, setAppeals] = useState<Appeal[]>([]);
  const [loading, setLoading] = useState(true);
  const [showAppealForm, setShowAppealForm] = useState<number | null>(null);
  const [appealText, setAppealText] = useState('');
  const [submittingAppeal, setSubmittingAppeal] = useState(false);

  useEffect(() => {
    if (status === 'loading') return;
    if (!session) {
      router.push('/auth/signin');
      return;
    }
    fetchRestrictionData();
  }, [session, status]);

  const fetchRestrictionData = async () => {
    try {
      const [restrictionsRes, appealsRes] = await Promise.all([
        fetch('/api/user/restrictions'),
        fetch('/api/user/appeals')
      ]);

      if (restrictionsRes.ok) {
        const restrictionsData = await restrictionsRes.json();
        setRestrictions(restrictionsData.restrictions || []);
      }

      if (appealsRes.ok) {
        const appealsData = await appealsRes.json();
        setAppeals(appealsData.appeals || []);
      }
    } catch (error) {
      console.error('Error fetching restriction data:', error);
      toast.error('Failed to load restriction information');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmitAppeal = async (restrictionId: number) => {
    if (!appealText.trim()) {
      toast.error('Please provide a reason for your appeal');
      return;
    }

    setSubmittingAppeal(true);
    try {
      const response = await fetch('/api/user/appeals', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          restrictionId,
          appealReason: appealText.trim()
        })
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to submit appeal');
      }

      toast.success('Appeal submitted successfully. We will review it within 3-5 business days.');
      setShowAppealForm(null);
      setAppealText('');
      fetchRestrictionData(); // Refresh data
    } catch (error) {
      toast.error(error instanceof Error ? error.message : 'Failed to submit appeal');
    } finally {
      setSubmittingAppeal(false);
    }
  };

  const getRestrictionTypeLabel = (type: string) => {
    const labels: Record<string, string> = {
      account_suspension: 'Account Suspension',
      booking_restrictions: 'Booking Restrictions',
      service_provider_restrictions: 'Service Provider Restrictions',
      communication_restrictions: 'Communication Restrictions',
      payment_restrictions: 'Payment Restrictions'
    };
    return labels[type] || type.replace('_', ' ');
  };

  const getRestrictionDescription = (type: string) => {
    const descriptions: Record<string, string> = {
      account_suspension: 'Your account access is temporarily or permanently suspended.',
      booking_restrictions: 'You cannot create new service bookings.',
      service_provider_restrictions: 'You cannot accept new booking requests.',
      communication_restrictions: 'Your messaging capabilities are limited.',
      payment_restrictions: 'Payment transactions require manual approval.'
    };
    return descriptions[type] || 'Specific restrictions apply to your account.';
  };

  const getSeverityColor = (severity: string) => {
    const colors: Record<string, string> = {
      minor: 'text-yellow-600 bg-yellow-50 border-yellow-200',
      moderate: 'text-orange-600 bg-orange-50 border-orange-200',
      severe: 'text-red-600 bg-red-50 border-red-200',
      critical: 'text-red-800 bg-red-100 border-red-300'
    };
    return colors[severity] || colors.moderate;
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <XCircle className="w-5 h-5 text-red-500" />;
      case 'expired':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'lifted':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      default:
        return <Info className="w-5 h-5 text-gray-500" />;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const isRestrictionActive = (restriction: Restriction) => {
    return restriction.status === 'active';
  };

  const canAppeal = (restriction: Restriction) => {
    if (!isRestrictionActive(restriction)) return false;
    if (restriction.appeal_deadline && new Date(restriction.appeal_deadline) < new Date()) return false;
    return !appeals.some(appeal => 
      appeal.restriction_id === restriction.id && 
      ['submitted', 'under_review'].includes(appeal.status)
    );
  };

  const activeRestrictions = restrictions.filter(isRestrictionActive);
  const inactiveRestrictions = restrictions.filter(r => !isRestrictionActive(r));

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-red-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading restriction status...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <button
            onClick={() => router.back()}
            className="inline-flex items-center text-gray-600 hover:text-gray-900 mb-4"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Dashboard
          </button>
          
          <div className="flex items-center">
            <AlertTriangle className="w-8 h-8 text-red-600 mr-3" />
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Account Restrictions</h1>
              <p className="text-gray-600 mt-1">
                {activeRestrictions.length > 0 
                  ? `You have ${activeRestrictions.length} active restriction${activeRestrictions.length > 1 ? 's' : ''}`
                  : 'Your account is in good standing'
                }
              </p>
            </div>
          </div>
        </div>

        {/* Active Restrictions */}
        {activeRestrictions.length > 0 && (
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Active Restrictions</h2>
            <div className="space-y-4">
              {activeRestrictions.map((restriction) => (
                <div key={restriction.id} className="bg-white rounded-xl shadow-md border border-red-200">
                  <div className="p-6">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center mb-3">
                          {getStatusIcon(restriction.status)}
                          <h3 className="text-lg font-semibold text-gray-900 ml-2">
                            {getRestrictionTypeLabel(restriction.restriction_type)}
                          </h3>
                          <span className={`ml-3 px-2 py-1 text-xs font-medium rounded-full border ${getSeverityColor(restriction.severity)}`}>
                            {restriction.severity.toUpperCase()}
                          </span>
                        </div>
                        
                        <p className="text-gray-600 mb-4">
                          {getRestrictionDescription(restriction.restriction_type)}
                        </p>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                          <div className="flex items-center text-sm text-gray-600">
                            <Calendar className="w-4 h-4 mr-2" />
                            <span>Started: {formatDate(restriction.start_date)}</span>
                          </div>
                          
                          {restriction.end_date && !restriction.is_permanent && (
                            <div className="flex items-center text-sm text-gray-600">
                              <Clock className="w-4 h-4 mr-2" />
                              <span>Ends: {formatDate(restriction.end_date)}</span>
                            </div>
                          )}
                          
                          {restriction.is_permanent && (
                            <div className="flex items-center text-sm text-red-600">
                              <XCircle className="w-4 h-4 mr-2" />
                              <span>Permanent Restriction</span>
                            </div>
                          )}
                        </div>

                        {restriction.custom_reason && (
                          <div className="bg-gray-50 rounded-lg p-4 mb-4">
                            <h4 className="font-medium text-gray-900 mb-2">Reason for Restriction:</h4>
                            <p className="text-gray-700 text-sm">{restriction.custom_reason}</p>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Appeal Section */}
                    <div className="border-t border-gray-200 pt-4 mt-4">
                      {canAppeal(restriction) ? (
                        <div>
                          {showAppealForm === restriction.id ? (
                            <div className="space-y-4">
                              <h4 className="font-medium text-gray-900">Submit an Appeal</h4>
                              <textarea
                                value={appealText}
                                onChange={(e) => setAppealText(e.target.value)}
                                placeholder="Please explain why you believe this restriction should be lifted..."
                                rows={4}
                                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                              />
                              <div className="flex space-x-3">
                                <button
                                  onClick={() => handleSubmitAppeal(restriction.id)}
                                  disabled={submittingAppeal || !appealText.trim()}
                                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
                                >
                                  {submittingAppeal ? 'Submitting...' : 'Submit Appeal'}
                                </button>
                                <button
                                  onClick={() => setShowAppealForm(null)}
                                  className="px-4 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400"
                                >
                                  Cancel
                                </button>
                              </div>
                            </div>
                          ) : (
                            <button
                              onClick={() => setShowAppealForm(restriction.id)}
                              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                            >
                              <FileText className="w-4 h-4 mr-2" />
                              Submit Appeal
                            </button>
                          )}
                        </div>
                      ) : (
                        <div className="text-sm text-gray-600">
                          {appeals.some(appeal => appeal.restriction_id === restriction.id) ? (
                            <div className="flex items-center">
                              <MessageSquare className="w-4 h-4 mr-2" />
                              Appeal already submitted and under review
                            </div>
                          ) : (
                            <div className="flex items-center">
                              <Clock className="w-4 h-4 mr-2" />
                              Appeal deadline has passed
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* No Active Restrictions */}
        {activeRestrictions.length === 0 && (
          <div className="bg-green-50 border border-green-200 rounded-xl p-6 mb-8">
            <div className="flex items-center">
              <CheckCircle className="w-6 h-6 text-green-600 mr-3" />
              <div>
                <h3 className="text-lg font-semibold text-green-900">Account in Good Standing</h3>
                <p className="text-green-700 mt-1">
                  Your account currently has no active restrictions. Continue following our community guidelines to maintain this status.
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Appeal History */}
        {appeals.length > 0 && (
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Appeal History</h2>
            <div className="bg-white rounded-xl shadow-md">
              <div className="p-6">
                <div className="space-y-4">
                  {appeals.map((appeal) => (
                    <div key={appeal.id} className="border-l-4 border-blue-500 pl-4">
                      <div className="flex items-center justify-between">
                        <h4 className="font-medium text-gray-900">
                          Appeal #{appeal.id}
                        </h4>
                        <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                          appeal.status === 'approved' ? 'bg-green-100 text-green-800' :
                          appeal.status === 'denied' ? 'bg-red-100 text-red-800' :
                          'bg-yellow-100 text-yellow-800'
                        }`}>
                          {appeal.status.toUpperCase()}
                        </span>
                      </div>
                      <p className="text-sm text-gray-600 mt-1">
                        Submitted: {formatDate(appeal.submitted_at)}
                      </p>
                      {appeal.admin_response && (
                        <div className="mt-3 p-3 bg-gray-50 rounded-lg">
                          <p className="text-sm text-gray-700">{appeal.admin_response}</p>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Contact Support */}
        <div className="bg-blue-50 border border-blue-200 rounded-xl p-6">
          <div className="flex items-start">
            <Info className="w-6 h-6 text-blue-600 mr-3 mt-0.5" />
            <div>
              <h3 className="text-lg font-semibold text-blue-900">Need Help?</h3>
              <p className="text-blue-700 mt-1 mb-3">
                If you have questions about your restrictions or need assistance with the appeal process, please contact our support team.
              </p>
              <button className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                <MessageSquare className="w-4 h-4 mr-2" />
                Contact Support
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
