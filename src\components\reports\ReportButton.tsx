'use client';

import React, { useState } from 'react';
import { useSession } from 'next-auth/react';
import { Flag, AlertTriangle } from 'lucide-react';
import ReportSubmissionForm from './ReportSubmissionForm';
import { toast } from 'react-hot-toast';

interface ReportButtonProps {
  reportedUserId: number;
  reportedUserName: string;
  bookingId?: number;
  variant?: 'button' | 'icon' | 'link';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export default function ReportButton({
  reportedUserId,
  reportedUserName,
  bookingId,
  variant = 'button',
  size = 'md',
  className = ''
}: ReportButtonProps) {
  const { data: session } = useSession();
  const [showReportForm, setShowReportForm] = useState(false);

  const handleReportClick = () => {
    if (!session?.user?.id) {
      toast.error('You must be logged in to report a user');
      return;
    }

    if (parseInt(session.user.id) === reportedUserId) {
      toast.error('You cannot report yourself');
      return;
    }

    setShowReportForm(true);
  };

  const handleReportSuccess = () => {
    // Optionally refresh data or show additional feedback
    toast.success('Thank you for your report. We will review it promptly.');
  };

  // Size classes
  const sizeClasses = {
    sm: {
      button: 'px-3 py-1.5 text-xs',
      icon: 'w-4 h-4',
      text: 'text-xs'
    },
    md: {
      button: 'px-4 py-2 text-sm',
      icon: 'w-5 h-5',
      text: 'text-sm'
    },
    lg: {
      button: 'px-6 py-3 text-base',
      icon: 'w-6 h-6',
      text: 'text-base'
    }
  };

  // Render different variants
  const renderButton = () => {
    switch (variant) {
      case 'icon':
        return (
          <button
            onClick={handleReportClick}
            className={`inline-flex items-center justify-center p-2 text-gray-500 hover:text-red-600 hover:bg-red-50 rounded-full transition-colors ${className}`}
            title={`Report ${reportedUserName}`}
          >
            <Flag className={sizeClasses[size].icon} />
          </button>
        );

      case 'link':
        return (
          <button
            onClick={handleReportClick}
            className={`inline-flex items-center text-gray-500 hover:text-red-600 transition-colors ${sizeClasses[size].text} ${className}`}
          >
            <Flag className={`${sizeClasses[size].icon} mr-1.5`} />
            Report User
          </button>
        );

      default: // button
        return (
          <button
            onClick={handleReportClick}
            className={`inline-flex items-center justify-center border border-gray-300 rounded-md font-medium text-gray-700 bg-white hover:bg-red-50 hover:text-red-600 hover:border-red-300 transition-colors ${sizeClasses[size].button} ${className}`}
          >
            <Flag className={`${sizeClasses[size].icon} mr-2`} />
            Report User
          </button>
        );
    }
  };

  return (
    <>
      {renderButton()}
      
      {showReportForm && (
        <ReportSubmissionForm
          reportedUserId={reportedUserId}
          reportedUserName={reportedUserName}
          bookingId={bookingId}
          onClose={() => setShowReportForm(false)}
          onSuccess={handleReportSuccess}
        />
      )}
    </>
  );
}

// Convenience component for reporting in the context of a booking
export function BookingReportButton({
  bookingId,
  reportedUserId,
  reportedUserName,
  variant = 'link',
  size = 'sm'
}: {
  bookingId: number;
  reportedUserId: number;
  reportedUserName: string;
  variant?: 'button' | 'icon' | 'link';
  size?: 'sm' | 'md' | 'lg';
}) {
  return (
    <ReportButton
      reportedUserId={reportedUserId}
      reportedUserName={reportedUserName}
      bookingId={bookingId}
      variant={variant}
      size={size}
    />
  );
}

// Convenience component for profile reporting
export function ProfileReportButton({
  userId,
  userName,
  variant = 'button',
  size = 'md'
}: {
  userId: number;
  userName: string;
  variant?: 'button' | 'icon' | 'link';
  size?: 'sm' | 'md' | 'lg';
}) {
  return (
    <ReportButton
      reportedUserId={userId}
      reportedUserName={userName}
      variant={variant}
      size={size}
    />
  );
}
