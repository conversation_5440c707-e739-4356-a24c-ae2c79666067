'use client';

import React, { useState } from 'react';
import { useSession } from 'next-auth/react';
import { toast } from 'react-hot-toast';
import { X, Upload, AlertTriangle, FileText, Image } from 'lucide-react';

interface ReportSubmissionFormProps {
  reportedUserId: number;
  reportedUserName: string;
  bookingId?: number;
  onClose: () => void;
  onSuccess?: () => void;
}

const REPORT_CATEGORIES = [
  { value: 'inappropriate_behavior', label: 'Inappropriate Behavior', description: 'Rude, offensive, or unprofessional conduct' },
  { value: 'spam', label: 'Spam', description: 'Unwanted promotional messages or repetitive content' },
  { value: 'fraudulent_activity', label: 'Fraudulent Activity', description: 'Scams, fake services, or deceptive practices' },
  { value: 'service_quality_issues', label: 'Service Quality Issues', description: 'Poor service delivery or unmet expectations' },
  { value: 'payment_disputes', label: 'Payment Disputes', description: 'Issues with payments, refunds, or billing' },
  { value: 'harassment', label: 'Harassment', description: 'Threatening, bullying, or persistent unwanted contact' },
  { value: 'fake_profiles', label: 'Fake Profiles', description: 'Impersonation or false identity' },
  { value: 'other_violations', label: 'Other Violations', description: 'Other policy violations not listed above' }
];

export default function ReportSubmissionForm({
  reportedUserId,
  reportedUserName,
  bookingId,
  onClose,
  onSuccess
}: ReportSubmissionFormProps) {
  const { data: session } = useSession();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    category: '',
    description: '',
    evidenceFiles: [] as File[]
  });

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    const validFiles = files.filter(file => {
      const isValidType = [
        'image/jpeg', 'image/png', 'image/gif', 'image/webp',
        'application/pdf', 'text/plain'
      ].includes(file.type);
      const isValidSize = file.size <= 10 * 1024 * 1024; // 10MB

      if (!isValidType) {
        toast.error(`File ${file.name} has an invalid type. Only images, PDFs, and text files are allowed.`);
        return false;
      }
      if (!isValidSize) {
        toast.error(`File ${file.name} is too large. Maximum size is 10MB.`);
        return false;
      }
      return true;
    });

    setFormData(prev => ({
      ...prev,
      evidenceFiles: [...prev.evidenceFiles, ...validFiles].slice(0, 5) // Max 5 files
    }));
  };

  const removeFile = (index: number) => {
    setFormData(prev => ({
      ...prev,
      evidenceFiles: prev.evidenceFiles.filter((_, i) => i !== index)
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!session?.user?.id) {
      toast.error('You must be logged in to submit a report');
      return;
    }

    if (!formData.category) {
      toast.error('Please select a report category');
      return;
    }

    if (formData.description.trim().length < 10) {
      toast.error('Please provide a detailed description (at least 10 characters)');
      return;
    }

    setIsSubmitting(true);

    try {
      const submitFormData = new FormData();
      submitFormData.append('reportedUserId', reportedUserId.toString());
      submitFormData.append('category', formData.category);
      submitFormData.append('description', formData.description.trim());
      
      if (bookingId) {
        submitFormData.append('bookingId', bookingId.toString());
      }

      // Add evidence files
      formData.evidenceFiles.forEach(file => {
        submitFormData.append('evidenceFiles', file);
      });

      const response = await fetch('/api/reports/submit', {
        method: 'POST',
        body: submitFormData
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to submit report');
      }

      toast.success('Report submitted successfully. Our admin team will review it shortly.');
      onSuccess?.();
      onClose();

    } catch (error) {
      console.error('Error submitting report:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to submit report');
    } finally {
      setIsSubmitting(false);
    }
  };

  const selectedCategory = REPORT_CATEGORIES.find(cat => cat.value === formData.category);

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">Report User</h2>
            <p className="text-sm text-gray-600 mt-1">
              Reporting: <span className="font-medium">{reportedUserName}</span>
              {bookingId && <span className="ml-2 text-blue-600">(Booking #{bookingId})</span>}
            </p>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Category Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">
              Report Category *
            </label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {REPORT_CATEGORIES.map((category) => (
                <label
                  key={category.value}
                  className={`relative flex flex-col p-4 border rounded-lg cursor-pointer transition-all ${
                    formData.category === category.value
                      ? 'border-red-500 bg-red-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <input
                    type="radio"
                    name="category"
                    value={category.value}
                    checked={formData.category === category.value}
                    onChange={(e) => setFormData(prev => ({ ...prev, category: e.target.value }))}
                    className="sr-only"
                  />
                  <span className="font-medium text-sm text-gray-900">{category.label}</span>
                  <span className="text-xs text-gray-600 mt-1">{category.description}</span>
                </label>
              ))}
            </div>
          </div>

          {/* Selected Category Info */}
          {selectedCategory && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <div className="flex items-start">
                <AlertTriangle className="w-5 h-5 text-yellow-600 mt-0.5 mr-3 flex-shrink-0" />
                <div>
                  <h4 className="font-medium text-yellow-800">{selectedCategory.label}</h4>
                  <p className="text-sm text-yellow-700 mt-1">{selectedCategory.description}</p>
                </div>
              </div>
            </div>
          )}

          {/* Description */}
          <div>
            <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
              Detailed Description *
            </label>
            <textarea
              id="description"
              rows={5}
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              placeholder="Please provide a detailed description of the issue. Include specific examples, dates, and any relevant context that will help our team understand the situation."
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500 resize-none"
              maxLength={2000}
            />
            <div className="flex justify-between items-center mt-2">
              <p className="text-xs text-gray-500">
                Minimum 10 characters required
              </p>
              <p className="text-xs text-gray-500">
                {formData.description.length}/2000
              </p>
            </div>
          </div>

          {/* File Upload */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Evidence Files (Optional)
            </label>
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
              <Upload className="w-8 h-8 text-gray-400 mx-auto mb-2" />
              <p className="text-sm text-gray-600 mb-2">
                Upload screenshots, documents, or other evidence
              </p>
              <input
                type="file"
                multiple
                accept="image/*,.pdf,.txt"
                onChange={handleFileChange}
                className="hidden"
                id="evidence-upload"
              />
              <label
                htmlFor="evidence-upload"
                className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 cursor-pointer"
              >
                Choose Files
              </label>
              <p className="text-xs text-gray-500 mt-2">
                Max 5 files, 10MB each. Supported: Images, PDF, Text files
              </p>
            </div>

            {/* File List */}
            {formData.evidenceFiles.length > 0 && (
              <div className="mt-4 space-y-2">
                {formData.evidenceFiles.map((file, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center">
                      {file.type.startsWith('image/') ? (
                        <Image className="w-5 h-5 text-blue-500 mr-3" />
                      ) : (
                        <FileText className="w-5 h-5 text-gray-500 mr-3" />
                      )}
                      <div>
                        <p className="text-sm font-medium text-gray-900">{file.name}</p>
                        <p className="text-xs text-gray-500">
                          {(file.size / 1024 / 1024).toFixed(2)} MB
                        </p>
                      </div>
                    </div>
                    <button
                      type="button"
                      onClick={() => removeFile(index)}
                      className="text-red-500 hover:text-red-700"
                    >
                      <X className="w-4 h-4" />
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Submit Buttons */}
          <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
              disabled={isSubmitting}
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isSubmitting || !formData.category || formData.description.trim().length < 10}
              className="px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isSubmitting ? 'Submitting...' : 'Submit Report'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
