-- Create comprehensive admin actions log for audit trail
CREATE TABLE IF NOT EXISTS admin_actions_log (
  id INT AUTO_INCREMENT PRIMARY KEY,
  admin_id INT NOT NULL,
  action_type ENUM(
    'report_created',
    'report_reviewed',
    'report_status_changed',
    'report_assigned',
    'restriction_applied',
    'restriction_modified',
    'restriction_lifted',
    'appeal_reviewed',
    'appeal_approved',
    'appeal_denied',
    'appeal_escalated',
    'user_communication',
    'bulk_action',
    'system_override',
    'data_export',
    'policy_update'
  ) NOT NULL,
  target_type ENUM(
    'user_report',
    'user_restriction', 
    'restriction_appeal',
    'user_account',
    'system_setting',
    'bulk_operation'
  ) NOT NULL,
  target_id INT DEFAULT NULL COMMENT 'ID of the target entity (report, restriction, appeal, etc.)',
  affected_user_id INT DEFAULT NULL COMMENT 'User affected by this action',
  action_details JSON NOT NULL COMMENT 'Detailed information about the action taken',
  previous_state JSON DEFAULT NULL COMMENT 'State before the action (for modifications)',
  new_state JSON DEFAULT NULL COMMENT 'State after the action (for modifications)',
  reason TEXT DEFAULT NULL COMMENT 'Admin reason for taking this action',
  ip_address VARCHAR(45) DEFAULT NULL COMMENT 'Admin IP address',
  user_agent TEXT DEFAULT NULL COMMENT 'Admin browser/client information',
  session_id VARCHAR(255) DEFAULT NULL COMMENT 'Admin session identifier',
  severity ENUM('low', 'medium', 'high', 'critical') DEFAULT 'medium',
  requires_approval BOOLEAN DEFAULT FALSE COMMENT 'Whether this action requires senior admin approval',
  approved_by_admin_id INT DEFAULT NULL COMMENT 'Senior admin who approved this action',
  approved_at TIMESTAMP NULL DEFAULT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
  -- Indexes for performance and audit queries
  INDEX idx_admin_id (admin_id),
  INDEX idx_action_type (action_type),
  INDEX idx_target_type_id (target_type, target_id),
  INDEX idx_affected_user (affected_user_id),
  INDEX idx_created_at (created_at),
  INDEX idx_severity (severity),
  INDEX idx_admin_activity (admin_id, created_at),
  INDEX idx_user_history (affected_user_id, created_at),
  INDEX idx_action_audit (action_type, target_type, created_at),
  INDEX idx_approval_queue (requires_approval, approved_at),
  
  -- Foreign key constraints
  FOREIGN KEY (admin_id) REFERENCES users(user_id) ON DELETE RESTRICT,
  FOREIGN KEY (affected_user_id) REFERENCES users(user_id) ON DELETE SET NULL,
  FOREIGN KEY (approved_by_admin_id) REFERENCES users(user_id) ON DELETE SET NULL,
  
  -- Constraints
  CONSTRAINT chk_approval_logic CHECK (
    (requires_approval = TRUE AND approved_by_admin_id IS NOT NULL AND approved_at IS NOT NULL) OR
    (requires_approval = FALSE) OR
    (requires_approval = TRUE AND approved_by_admin_id IS NULL AND approved_at IS NULL)
  )
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create system metrics tracking table
CREATE TABLE IF NOT EXISTS restriction_metrics (
  id INT AUTO_INCREMENT PRIMARY KEY,
  metric_date DATE NOT NULL,
  total_reports INT DEFAULT 0,
  reports_resolved INT DEFAULT 0,
  restrictions_applied INT DEFAULT 0,
  restrictions_lifted INT DEFAULT 0,
  appeals_submitted INT DEFAULT 0,
  appeals_approved INT DEFAULT 0,
  appeals_denied INT DEFAULT 0,
  average_resolution_time_hours DECIMAL(10,2) DEFAULT NULL,
  repeat_offenders INT DEFAULT 0,
  policy_violations_by_category JSON DEFAULT NULL,
  admin_workload_distribution JSON DEFAULT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  UNIQUE KEY unique_metric_date (metric_date),
  INDEX idx_metric_date (metric_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Add table comments
ALTER TABLE admin_actions_log COMMENT = 'Comprehensive audit log for all admin actions in the restriction system';
ALTER TABLE restriction_metrics COMMENT = 'Daily metrics and analytics for the restriction management system';
