-- Create restriction_appeals table for managing user appeals
CREATE TABLE IF NOT EXISTS restriction_appeals (
  id INT AUTO_INCREMENT PRIMARY KEY,
  restriction_id INT NOT NULL,
  user_id INT NOT NULL,
  appeal_reason TEXT NOT NULL COMMENT 'User explanation for why restriction should be lifted',
  supporting_documents JSON DEFAULT NULL COMMENT 'Array of file paths for supporting evidence',
  status ENUM(
    'submitted',
    'under_review', 
    'additional_info_requested',
    'approved',
    'denied',
    'escalated',
    'withdrawn'
  ) DEFAULT 'submitted',
  priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
  assigned_admin_id INT DEFAULT NULL COMMENT 'Admin reviewing the appeal',
  admin_response TEXT DEFAULT NULL COMMENT 'Admin response to the appeal',
  internal_notes TEXT DEFAULT NULL COMMENT 'Internal admin notes not visible to user',
  escalation_reason TEXT DEFAULT NULL COMMENT 'Reason for escalating to senior admin',
  escalated_to_admin_id INT DEFAULT NULL COMMENT 'Senior admin handling escalated appeal',
  submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  reviewed_at TIMESTAMP NULL DEFAULT NULL,
  decision_at TIMESTAMP NULL DEFAULT NULL,
  appeal_deadline TIMESTAMP NULL DEFAULT NULL COMMENT 'Deadline for this appeal',
  response_deadline TIMESTAMP NULL DEFAULT NULL COMMENT 'Admin response deadline',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  -- Indexes for performance
  INDEX idx_restriction_id (restriction_id),
  INDEX idx_user_id (user_id),
  INDEX idx_status (status),
  INDEX idx_assigned_admin (assigned_admin_id),
  INDEX idx_escalated_admin (escalated_to_admin_id),
  INDEX idx_submitted_at (submitted_at),
  INDEX idx_deadlines (appeal_deadline, response_deadline),
  INDEX idx_pending_review (status, assigned_admin_id),
  
  -- Foreign key constraints
  FOREIGN KEY (restriction_id) REFERENCES user_restrictions(id) ON DELETE CASCADE,
  FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
  FOREIGN KEY (assigned_admin_id) REFERENCES users(user_id) ON DELETE SET NULL,
  FOREIGN KEY (escalated_to_admin_id) REFERENCES users(user_id) ON DELETE SET NULL,
  
  -- Constraints
  CONSTRAINT chk_decision_timing CHECK (
    (status IN ('approved', 'denied') AND decision_at IS NOT NULL) OR
    (status NOT IN ('approved', 'denied'))
  )
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create appeal communication threads table
CREATE TABLE IF NOT EXISTS appeal_communications (
  id INT AUTO_INCREMENT PRIMARY KEY,
  appeal_id INT NOT NULL,
  sender_id INT NOT NULL,
  sender_type ENUM('user', 'admin') NOT NULL,
  message TEXT NOT NULL,
  attachments JSON DEFAULT NULL COMMENT 'Array of file paths for message attachments',
  is_internal BOOLEAN DEFAULT FALSE COMMENT 'Internal admin messages not visible to user',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
  INDEX idx_appeal_id (appeal_id),
  INDEX idx_sender_id (sender_id),
  INDEX idx_created_at (created_at),
  INDEX idx_appeal_timeline (appeal_id, created_at),
  
  FOREIGN KEY (appeal_id) REFERENCES restriction_appeals(id) ON DELETE CASCADE,
  FOREIGN KEY (sender_id) REFERENCES users(user_id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Add table comments
ALTER TABLE restriction_appeals COMMENT = 'Manages user appeals for account restrictions';
ALTER TABLE appeal_communications COMMENT = 'Communication threads between users and admins for appeals';
