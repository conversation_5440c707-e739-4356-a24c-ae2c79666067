-- Create user_reports table for storing user reports and violations
CREATE TABLE IF NOT EXISTS user_reports (
  id INT AUTO_INCREMENT PRIMARY KEY,
  reporter_id INT NOT NULL,
  reported_user_id INT NOT NULL,
  category ENUM(
    'inappropriate_behavior',
    'spam',
    'fraudulent_activity', 
    'service_quality_issues',
    'payment_disputes',
    'harassment',
    'fake_profiles',
    'other_violations'
  ) NOT NULL,
  description TEXT NOT NULL,
  evidence_files JSON DEFAULT NULL COMMENT 'Array of file paths for evidence attachments',
  booking_id INT DEFAULT NULL COMMENT 'Related booking ID if applicable',
  status ENUM('pending', 'under_review', 'resolved', 'dismissed') DEFAULT 'pending',
  admin_notes TEXT DEFAULT NULL COMMENT 'Internal admin notes for investigation',
  resolution_notes TEXT DEFAULT NULL COMMENT 'Notes about how the report was resolved',
  assigned_admin_id INT DEFAULT NULL COMMENT 'Admin assigned to review this report',
  priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  resolved_at TIMESTAMP NULL DEFAULT NULL,
  
  -- Indexes for performance
  INDEX idx_reporter_id (reporter_id),
  INDEX idx_reported_user_id (reported_user_id),
  INDEX idx_category (category),
  INDEX idx_status (status),
  INDEX idx_booking_id (booking_id),
  INDEX idx_assigned_admin_id (assigned_admin_id),
  INDEX idx_created_at (created_at),
  INDEX idx_priority_status (priority, status),
  
  -- Foreign key constraints
  FOREIGN KEY (reporter_id) REFERENCES users(user_id) ON DELETE CASCADE,
  FOREIGN KEY (reported_user_id) REFERENCES users(user_id) ON DELETE CASCADE,
  FOREIGN KEY (booking_id) REFERENCES service_bookings(id) ON DELETE SET NULL,
  FOREIGN KEY (assigned_admin_id) REFERENCES users(user_id) ON DELETE SET NULL,
  
  -- Constraints
  CONSTRAINT chk_different_users CHECK (reporter_id != reported_user_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create index for complex queries
CREATE INDEX idx_reports_admin_review ON user_reports (status, priority, created_at);
CREATE INDEX idx_reports_user_history ON user_reports (reported_user_id, created_at);

-- Add comments for documentation
ALTER TABLE user_reports COMMENT = 'Stores user reports for policy violations and inappropriate behavior';
