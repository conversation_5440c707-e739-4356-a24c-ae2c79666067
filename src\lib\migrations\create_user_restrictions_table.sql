-- Create user_restrictions table for managing user account restrictions
CREATE TABLE IF NOT EXISTS user_restrictions (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL,
  restriction_type ENUM(
    'account_suspension',
    'booking_restrictions', 
    'service_provider_restrictions',
    'communication_restrictions',
    'payment_restrictions',
    'profile_restrictions',
    'review_restrictions'
  ) NOT NULL,
  reason_category ENUM(
    'policy_violation',
    'inappropriate_behavior',
    'spam_activity',
    'fraudulent_activity',
    'payment_issues',
    'service_quality',
    'harassment',
    'fake_profile',
    'repeated_violations',
    'legal_compliance',
    'other'
  ) NOT NULL,
  custom_reason TEXT DEFAULT NULL COMMENT 'Additional details about the restriction reason',
  start_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  end_date TIMESTAMP NULL DEFAULT NULL COMMENT 'NULL for permanent restrictions',
  is_permanent BOOLEAN DEFAULT FALSE,
  applied_by_admin_id INT NOT NULL,
  status ENUM('active', 'expired', 'lifted', 'suspended') DEFAULT 'active',
  severity ENUM('minor', 'moderate', 'severe', 'critical') DEFAULT 'moderate',
  related_report_id INT DEFAULT NULL COMMENT 'Report that triggered this restriction',
  admin_notes TEXT DEFAULT NULL COMMENT 'Internal admin notes',
  user_notified BOOLEAN DEFAULT FALSE COMMENT 'Whether user has been notified',
  appeal_deadline TIMESTAMP NULL DEFAULT NULL COMMENT 'Deadline for submitting appeals',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  lifted_at TIMESTAMP NULL DEFAULT NULL,
  lifted_by_admin_id INT DEFAULT NULL,
  lift_reason TEXT DEFAULT NULL,
  
  -- Indexes for performance
  INDEX idx_user_id (user_id),
  INDEX idx_restriction_type (restriction_type),
  INDEX idx_status (status),
  INDEX idx_applied_by_admin (applied_by_admin_id),
  INDEX idx_start_end_dates (start_date, end_date),
  INDEX idx_related_report (related_report_id),
  INDEX idx_active_restrictions (user_id, status, end_date),
  INDEX idx_expiry_check (status, end_date, is_permanent),
  
  -- Foreign key constraints
  FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
  FOREIGN KEY (applied_by_admin_id) REFERENCES users(user_id) ON DELETE RESTRICT,
  FOREIGN KEY (lifted_by_admin_id) REFERENCES users(user_id) ON DELETE SET NULL,
  FOREIGN KEY (related_report_id) REFERENCES user_reports(id) ON DELETE SET NULL,
  
  -- Constraints
  CONSTRAINT chk_end_date_logic CHECK (
    (is_permanent = TRUE AND end_date IS NULL) OR
    (is_permanent = FALSE)
  ),
  CONSTRAINT chk_dates_order CHECK (
    end_date IS NULL OR end_date > start_date
  )
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Add table comment
ALTER TABLE user_restrictions COMMENT = 'Manages active and historical user account restrictions';
