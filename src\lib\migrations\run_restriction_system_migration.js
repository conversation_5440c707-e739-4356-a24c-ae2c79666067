// Database migration script for User Reporting and Restriction Management System
const mysql = require('mysql2/promise');
const fs = require('fs').promises;
const path = require('path');
require('dotenv').config();

class RestrictionSystemMigration {
  constructor() {
    this.connection = null;
    this.migrationFiles = [
      'create_user_reports_table.sql',
      'create_user_restrictions_table.sql', 
      'create_restriction_appeals_table.sql',
      'create_admin_actions_log_table.sql'
    ];
  }

  async init() {
    try {
      this.connection = await mysql.createConnection({
        host: process.env.DB_HOST || 'localhost',
        user: process.env.DB_USER || 'root',
        password: process.env.DB_PASSWORD || '',
        database: process.env.DB_NAME || 'rainbow_paws'
      });
      console.log('✅ Database connection established');
    } catch (error) {
      console.error('❌ Database connection failed:', error.message);
      throw error;
    }
  }

  async cleanup() {
    if (this.connection) {
      await this.connection.end();
    }
  }

  async executeSQLFile(filename) {
    try {
      const filePath = path.join(__dirname, filename);
      const sqlContent = await fs.readFile(filePath, 'utf8');

      // Remove comments and split by semicolon
      const cleanedSQL = sqlContent
        .replace(/--.*$/gm, '') // Remove line comments
        .replace(/\/\*[\s\S]*?\*\//g, '') // Remove block comments
        .replace(/\s+/g, ' ') // Normalize whitespace
        .trim();

      // Split by semicolon and execute each statement
      const statements = cleanedSQL
        .split(';')
        .map(stmt => stmt.trim())
        .filter(stmt => stmt.length > 0);

      for (const statement of statements) {
        if (statement.trim()) {
          try {
            await this.connection.execute(statement);
          } catch (error) {
            // Skip errors for existing tables/indexes
            if (!error.message.includes('already exists') &&
                !error.message.includes('Duplicate key name') &&
                !error.message.includes('Duplicate column name')) {
              throw error;
            }
          }
        }
      }

      console.log(`✅ Executed ${filename}`);
    } catch (error) {
      console.error(`❌ Error executing ${filename}:`, error.message);
      throw error;
    }
  }

  async updateUsersTable() {
    console.log('\n🔧 Updating users table with restriction fields...');
    
    try {
      // Check if restriction_status column exists
      const [columns] = await this.connection.execute(`
        SELECT column_name
        FROM information_schema.columns
        WHERE table_schema = DATABASE()
        AND table_name = 'users'
        AND column_name IN ('restriction_status', 'last_restriction_check')
      `);

      const existingColumns = columns.map(col => col.column_name);

      if (!existingColumns.includes('restriction_status')) {
        await this.connection.execute(`
          ALTER TABLE users 
          ADD COLUMN restriction_status ENUM('none', 'restricted', 'suspended') DEFAULT 'none'
          AFTER status
        `);
        console.log('✅ Added restriction_status column to users table');
      }

      if (!existingColumns.includes('last_restriction_check')) {
        await this.connection.execute(`
          ALTER TABLE users 
          ADD COLUMN last_restriction_check TIMESTAMP NULL DEFAULT NULL
          AFTER restriction_status
        `);
        console.log('✅ Added last_restriction_check column to users table');
      }

      // Add index for restriction queries
      try {
        await this.connection.execute(`
          CREATE INDEX idx_users_restriction_status ON users (restriction_status, last_restriction_check)
        `);
        console.log('✅ Added restriction status index');
      } catch (error) {
        if (!error.message.includes('Duplicate key name')) {
          throw error;
        }
        console.log('✅ Restriction status index already exists');
      }

    } catch (error) {
      console.error('❌ Error updating users table:', error.message);
      throw error;
    }
  }

  async createMigrationTrackingTable() {
    console.log('\n🔧 Creating migration tracking table...');
    
    try {
      await this.connection.execute(`
        CREATE TABLE IF NOT EXISTS migration_history (
          id INT AUTO_INCREMENT PRIMARY KEY,
          migration_name VARCHAR(255) NOT NULL UNIQUE,
          executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          success BOOLEAN DEFAULT TRUE,
          error_message TEXT DEFAULT NULL,
          
          INDEX idx_migration_name (migration_name),
          INDEX idx_executed_at (executed_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
      `);
      
      console.log('✅ Migration tracking table ready');
    } catch (error) {
      console.error('❌ Error creating migration tracking table:', error.message);
      throw error;
    }
  }

  async recordMigration(migrationName, success = true, errorMessage = null) {
    try {
      await this.connection.execute(`
        INSERT INTO migration_history (migration_name, success, error_message)
        VALUES (?, ?, ?)
        ON DUPLICATE KEY UPDATE
        executed_at = CURRENT_TIMESTAMP,
        success = VALUES(success),
        error_message = VALUES(error_message)
      `, [migrationName, success, errorMessage]);
    } catch (error) {
      console.error('Error recording migration:', error.message);
    }
  }

  async checkExistingMigrations() {
    try {
      const [migrations] = await this.connection.execute(`
        SELECT migration_name, success 
        FROM migration_history 
        WHERE migration_name LIKE 'restriction_system_%'
      `);
      
      return migrations.reduce((acc, migration) => {
        acc[migration.migration_name] = migration.success;
        return acc;
      }, {});
    } catch (error) {
      // Table doesn't exist yet
      return {};
    }
  }

  async runMigrations() {
    console.log('🚀 Starting Restriction System Database Migration\n');
    
    await this.init();

    try {
      // Create migration tracking
      await this.createMigrationTrackingTable();
      
      // Check existing migrations
      const existingMigrations = await this.checkExistingMigrations();
      
      // Update users table first
      if (!existingMigrations['restriction_system_users_update']) {
        await this.updateUsersTable();
        await this.recordMigration('restriction_system_users_update');
      } else {
        console.log('✅ Users table update already completed');
      }

      // Execute migration files
      for (const filename of this.migrationFiles) {
        const migrationName = `restriction_system_${filename.replace('.sql', '')}`;
        
        if (!existingMigrations[migrationName]) {
          console.log(`\n🔧 Executing ${filename}...`);
          await this.executeSQLFile(filename);
          await this.recordMigration(migrationName);
        } else {
          console.log(`✅ ${filename} already executed`);
        }
      }

      // Verify tables were created
      await this.verifyTables();

      console.log('\n✅ Restriction System Database Migration Completed Successfully!');
      
    } catch (error) {
      console.error('\n❌ Migration failed:', error.message);
      throw error;
    } finally {
      await this.cleanup();
    }
  }

  async verifyTables() {
    console.log('\n🔍 Verifying table creation...');
    
    const expectedTables = [
      'user_reports',
      'user_restrictions', 
      'restriction_appeals',
      'appeal_communications',
      'admin_actions_log',
      'restriction_metrics'
    ];

    for (const tableName of expectedTables) {
      const [tables] = await this.connection.execute(`
        SELECT COUNT(*) as count
        FROM information_schema.tables
        WHERE table_schema = DATABASE()
        AND table_name = ?
      `, [tableName]);

      if (tables[0].count > 0) {
        console.log(`✅ ${tableName} table created successfully`);
      } else {
        throw new Error(`Table ${tableName} was not created`);
      }
    }
  }
}

// Run migration if called directly
if (require.main === module) {
  const migration = new RestrictionSystemMigration();
  migration.runMigrations()
    .then(() => {
      console.log('\n🎯 Database is ready for the Restriction Management System!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Migration failed:', error.message);
      process.exit(1);
    });
}

module.exports = RestrictionSystemMigration;
