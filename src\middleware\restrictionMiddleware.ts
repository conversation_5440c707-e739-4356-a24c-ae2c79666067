import { NextRequest, NextResponse } from 'next/server';
import { getToken } from 'next-auth/jwt';
import { db } from '@/lib/db';

// Routes that restricted users should be redirected from
const RESTRICTED_ROUTES = [
  '/dashboard',
  '/bookings',
  '/services',
  '/profile',
  '/messages',
  '/payments'
];

// Routes that restricted users can still access
const ALLOWED_ROUTES = [
  '/user/restriction-status',
  '/auth',
  '/api/auth',
  '/api/user/restrictions',
  '/api/user/appeals',
  '/_next',
  '/favicon.ico'
];

export async function checkUserRestrictions(request: NextRequest) {
  try {
    const token = await getToken({ req: request });
    
    if (!token?.sub) {
      // User not authenticated, let them proceed
      return NextResponse.next();
    }

    const userId = parseInt(token.sub);
    const pathname = request.nextUrl.pathname;

    // Allow access to certain routes regardless of restrictions
    if (ALLOWED_ROUTES.some(route => pathname.startsWith(route))) {
      return NextResponse.next();
    }

    // Check if user has active restrictions
    const [restrictions] = await db.execute(`
      SELECT 
        id, 
        restriction_type, 
        status, 
        end_date, 
        is_permanent,
        severity
      FROM user_restrictions
      WHERE user_id = ? 
      AND status = 'active'
      AND (end_date IS NULL OR end_date > NOW() OR is_permanent = TRUE)
    `, [userId]);

    if (!Array.isArray(restrictions) || restrictions.length === 0) {
      // No active restrictions, proceed normally
      return NextResponse.next();
    }

    const activeRestrictions = restrictions as any[];

    // Check for account suspension (blocks all access)
    const accountSuspension = activeRestrictions.find(r => 
      r.restriction_type === 'account_suspension'
    );

    if (accountSuspension) {
      // Redirect to restriction status page for suspended accounts
      if (!pathname.startsWith('/user/restriction-status')) {
        const url = request.nextUrl.clone();
        url.pathname = '/user/restriction-status';
        return NextResponse.redirect(url);
      }
    }

    // Check specific route restrictions
    const routeRestrictions = getRouteRestrictions(pathname, activeRestrictions);
    
    if (routeRestrictions.length > 0) {
      // User is trying to access a restricted route
      if (!pathname.startsWith('/user/restriction-status')) {
        const url = request.nextUrl.clone();
        url.pathname = '/user/restriction-status';
        url.searchParams.set('blocked_route', pathname);
        return NextResponse.redirect(url);
      }
    }

    // Add restriction headers for the frontend to use
    const response = NextResponse.next();
    response.headers.set('X-User-Restrictions', JSON.stringify(
      activeRestrictions.map(r => ({
        type: r.restriction_type,
        severity: r.severity
      }))
    ));

    return response;

  } catch (error) {
    console.error('Error checking user restrictions:', error);
    // On error, allow the request to proceed to avoid breaking the app
    return NextResponse.next();
  }
}

function getRouteRestrictions(pathname: string, restrictions: any[]) {
  const applicableRestrictions = [];

  for (const restriction of restrictions) {
    switch (restriction.restriction_type) {
      case 'booking_restrictions':
        if (pathname.startsWith('/bookings') || pathname.startsWith('/book-service')) {
          applicableRestrictions.push(restriction);
        }
        break;

      case 'service_provider_restrictions':
        if (pathname.startsWith('/services') || pathname.startsWith('/provider')) {
          applicableRestrictions.push(restriction);
        }
        break;

      case 'communication_restrictions':
        if (pathname.startsWith('/messages') || pathname.startsWith('/chat')) {
          applicableRestrictions.push(restriction);
        }
        break;

      case 'payment_restrictions':
        if (pathname.startsWith('/payments') || pathname.startsWith('/billing')) {
          applicableRestrictions.push(restriction);
        }
        break;

      default:
        // For unknown restriction types, be conservative and block access
        if (RESTRICTED_ROUTES.some(route => pathname.startsWith(route))) {
          applicableRestrictions.push(restriction);
        }
        break;
    }
  }

  return applicableRestrictions;
}

// Helper function to check restrictions via API
export async function checkUserRestrictionAPI(userId: number, restrictionType?: string) {
  try {
    let query = `
      SELECT 
        id, 
        restriction_type, 
        status, 
        end_date, 
        is_permanent,
        severity,
        custom_reason
      FROM user_restrictions
      WHERE user_id = ? 
      AND status = 'active'
      AND (end_date IS NULL OR end_date > NOW() OR is_permanent = TRUE)
    `;
    
    const params = [userId];

    if (restrictionType) {
      query += ' AND restriction_type = ?';
      params.push(restrictionType);
    }

    const [restrictions] = await db.execute(query, params);

    return {
      hasRestrictions: Array.isArray(restrictions) && restrictions.length > 0,
      restrictions: restrictions as any[],
      isBlocked: (restrictions as any[]).some(r => 
        r.restriction_type === 'account_suspension' ||
        (restrictionType && r.restriction_type === restrictionType)
      )
    };

  } catch (error) {
    console.error('Error checking user restrictions via API:', error);
    return {
      hasRestrictions: false,
      restrictions: [],
      isBlocked: false
    };
  }
}

// Middleware function to be used in API routes
export function withRestrictionCheck(
  handler: (req: NextRequest, context: any) => Promise<NextResponse>,
  allowedRestrictions: string[] = []
) {
  return async (req: NextRequest, context: any) => {
    try {
      const token = await getToken({ req });
      
      if (!token?.sub) {
        return NextResponse.json(
          { error: 'Authentication required' },
          { status: 401 }
        );
      }

      const userId = parseInt(token.sub);
      const restrictionCheck = await checkUserRestrictionAPI(userId);

      // Check for account suspension (blocks all API access except restriction-related)
      const accountSuspension = restrictionCheck.restrictions.find(r => 
        r.restriction_type === 'account_suspension'
      );

      if (accountSuspension && !allowedRestrictions.includes('account_suspension')) {
        return NextResponse.json(
          { 
            error: 'Account suspended',
            restriction: {
              type: 'account_suspension',
              reason: accountSuspension.custom_reason,
              severity: accountSuspension.severity
            }
          },
          { status: 403 }
        );
      }

      // Check for specific restrictions
      const blockedRestrictions = restrictionCheck.restrictions.filter(r => 
        !allowedRestrictions.includes(r.restriction_type)
      );

      if (blockedRestrictions.length > 0) {
        return NextResponse.json(
          { 
            error: 'Action restricted',
            restrictions: blockedRestrictions.map(r => ({
              type: r.restriction_type,
              reason: r.custom_reason,
              severity: r.severity
            }))
          },
          { status: 403 }
        );
      }

      // Add restriction info to request context
      (req as any).userRestrictions = restrictionCheck.restrictions;

      return handler(req, context);

    } catch (error) {
      console.error('Error in restriction middleware:', error);
      return NextResponse.json(
        { error: 'Internal server error' },
        { status: 500 }
      );
    }
  };
}
