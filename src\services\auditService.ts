import { db } from '@/lib/db';

interface AdminActionLog {
  adminId: number;
  actionType: 'report_created' | 'report_reviewed' | 'report_status_changed' | 'report_assigned' | 
             'restriction_applied' | 'restriction_modified' | 'restriction_lifted' | 
             'appeal_reviewed' | 'appeal_approved' | 'appeal_denied' | 'appeal_escalated' |
             'user_communication' | 'bulk_action' | 'system_override' | 'data_export' | 'policy_update';
  targetType: 'user_report' | 'user_restriction' | 'restriction_appeal' | 'user_account' | 'system_setting' | 'bulk_operation';
  targetId?: number;
  affectedUserId?: number;
  actionDetails: Record<string, any>;
  previousState?: Record<string, any>;
  newState?: Record<string, any>;
  reason?: string;
  severity?: 'low' | 'medium' | 'high' | 'critical';
  requiresApproval?: boolean;
}

export async function logAdminAction(actionData: AdminActionLog): Promise<number> {
  try {
    const {
      adminId,
      actionType,
      targetType,
      targetId,
      affectedUserId,
      actionDetails,
      previousState,
      newState,
      reason,
      severity = 'medium',
      requiresApproval = false
    } = actionData;

    // Get admin's IP and user agent from request context if available
    // For now, we'll set these as null since we don't have access to request context
    const ipAddress = null;
    const userAgent = null;
    const sessionId = null;

    const [result] = await db.execute(`
      INSERT INTO admin_actions_log (
        admin_id,
        action_type,
        target_type,
        target_id,
        affected_user_id,
        action_details,
        previous_state,
        new_state,
        reason,
        ip_address,
        user_agent,
        session_id,
        severity,
        requires_approval,
        created_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
    `, [
      adminId,
      actionType,
      targetType,
      targetId || null,
      affectedUserId || null,
      JSON.stringify(actionDetails),
      previousState ? JSON.stringify(previousState) : null,
      newState ? JSON.stringify(newState) : null,
      reason || null,
      ipAddress,
      userAgent,
      sessionId,
      severity,
      requiresApproval
    ]);

    const logId = (result as any).insertId;

    // If action requires approval, create a notification for senior admins
    if (requiresApproval) {
      await createApprovalNotification(logId, actionData);
    }

    return logId;

  } catch (error) {
    console.error('Error logging admin action:', error);
    throw new Error('Failed to log admin action');
  }
}

async function createApprovalNotification(logId: number, actionData: AdminActionLog) {
  try {
    // Get senior admins (you might want to add a role hierarchy system)
    const [seniorAdmins] = await db.execute(`
      SELECT user_id FROM users 
      WHERE role = 'admin' AND user_id != ?
      LIMIT 5
    `, [actionData.adminId]);

    // Create notifications for senior admins
    for (const admin of seniorAdmins as any[]) {
      await db.execute(`
        INSERT INTO admin_notifications (
          type, title, message, data, is_read, created_at
        ) VALUES (?, ?, ?, ?, 0, NOW())
      `, [
        'approval_required',
        'Admin Action Requires Approval',
        `Action "${actionData.actionType}" by admin ${actionData.adminId} requires your approval`,
        JSON.stringify({ logId, actionData })
      ]);
    }
  } catch (error) {
    console.error('Error creating approval notification:', error);
  }
}

export async function getAdminActionHistory(
  filters: {
    adminId?: number;
    actionType?: string;
    targetType?: string;
    affectedUserId?: number;
    startDate?: Date;
    endDate?: Date;
    severity?: string;
    page?: number;
    limit?: number;
  } = {}
) {
  try {
    const {
      adminId,
      actionType,
      targetType,
      affectedUserId,
      startDate,
      endDate,
      severity,
      page = 1,
      limit = 50
    } = filters;

    let whereConditions = [];
    let queryParams = [];

    if (adminId) {
      whereConditions.push('admin_id = ?');
      queryParams.push(adminId);
    }

    if (actionType) {
      whereConditions.push('action_type = ?');
      queryParams.push(actionType);
    }

    if (targetType) {
      whereConditions.push('target_type = ?');
      queryParams.push(targetType);
    }

    if (affectedUserId) {
      whereConditions.push('affected_user_id = ?');
      queryParams.push(affectedUserId);
    }

    if (startDate) {
      whereConditions.push('created_at >= ?');
      queryParams.push(startDate);
    }

    if (endDate) {
      whereConditions.push('created_at <= ?');
      queryParams.push(endDate);
    }

    if (severity) {
      whereConditions.push('severity = ?');
      queryParams.push(severity);
    }

    const whereClause = whereConditions.length > 0 ? 'WHERE ' + whereConditions.join(' AND ') : '';
    const offset = (page - 1) * limit;

    const [logs] = await db.execute(`
      SELECT 
        l.*,
        u.first_name as admin_first_name,
        u.last_name as admin_last_name,
        affected.first_name as affected_first_name,
        affected.last_name as affected_last_name
      FROM admin_actions_log l
      JOIN users u ON l.admin_id = u.user_id
      LEFT JOIN users affected ON l.affected_user_id = affected.user_id
      ${whereClause}
      ORDER BY l.created_at DESC
      LIMIT ? OFFSET ?
    `, [...queryParams, limit, offset]);

    // Get total count
    const [countResult] = await db.execute(`
      SELECT COUNT(*) as total
      FROM admin_actions_log l
      ${whereClause}
    `, queryParams);

    const total = (countResult as any)[0].total;

    return {
      logs: (logs as any[]).map(log => ({
        ...log,
        action_details: JSON.parse(log.action_details),
        previous_state: log.previous_state ? JSON.parse(log.previous_state) : null,
        new_state: log.new_state ? JSON.parse(log.new_state) : null,
        admin_name: `${log.admin_first_name} ${log.admin_last_name}`,
        affected_user_name: log.affected_first_name ? `${log.affected_first_name} ${log.affected_last_name}` : null
      })),
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    };

  } catch (error) {
    console.error('Error fetching admin action history:', error);
    throw new Error('Failed to fetch admin action history');
  }
}

export async function approveAdminAction(logId: number, approvingAdminId: number, approved: boolean, notes?: string) {
  try {
    await db.execute(`
      UPDATE admin_actions_log 
      SET 
        approved_by_admin_id = ?,
        approved_at = NOW(),
        reason = CONCAT(COALESCE(reason, ''), ' | Approval: ', ?, COALESCE(?, ''))
      WHERE id = ? AND requires_approval = TRUE
    `, [approvingAdminId, approved ? 'APPROVED' : 'DENIED', notes || '', logId]);

    // Log the approval action itself
    await logAdminAction({
      adminId: approvingAdminId,
      actionType: approved ? 'system_override' : 'system_override',
      targetType: 'system_setting',
      targetId: logId,
      actionDetails: {
        action: approved ? 'approved' : 'denied',
        originalLogId: logId,
        notes
      },
      reason: `${approved ? 'Approved' : 'Denied'} admin action ${logId}`
    });

    return true;
  } catch (error) {
    console.error('Error approving admin action:', error);
    throw new Error('Failed to approve admin action');
  }
}
