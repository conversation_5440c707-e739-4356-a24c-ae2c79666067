// Restriction types and categories definitions

export enum RestrictionType {
  ACCOUNT_SUSPENSION = 'account_suspension',
  BOOKING_RESTRICTIONS = 'booking_restrictions',
  SERVICE_PROVIDER_RESTRICTIONS = 'service_provider_restrictions',
  COMMUNICATION_RESTRICTIONS = 'communication_restrictions',
  PAYMENT_RESTRICTIONS = 'payment_restrictions',
  PROFILE_RESTRICTIONS = 'profile_restrictions',
  REVIEW_RESTRICTIONS = 'review_restrictions'
}

export enum RestrictionReasonCategory {
  POLICY_VIOLATION = 'policy_violation',
  INAPPROPRIATE_BEHAVIOR = 'inappropriate_behavior',
  SPAM_ACTIVITY = 'spam_activity',
  FRAUDULENT_ACTIVITY = 'fraudulent_activity',
  PAYMENT_ISSUES = 'payment_issues',
  SERVICE_QUALITY = 'service_quality',
  HARASSMENT = 'harassment',
  FAKE_PROFILE = 'fake_profile',
  REPEATED_VIOLATIONS = 'repeated_violations',
  LEGAL_COMPLIANCE = 'legal_compliance',
  OTHER = 'other'
}

export enum RestrictionStatus {
  ACTIVE = 'active',
  EXPIRED = 'expired',
  LIFTED = 'lifted',
  SUSPENDED = 'suspended'
}

export enum RestrictionSeverity {
  MINOR = 'minor',
  MODERATE = 'moderate',
  SEVERE = 'severe',
  CRITICAL = 'critical'
}

export interface RestrictionTypeInfo {
  type: RestrictionType;
  label: string;
  description: string;
  impact: string[];
  defaultDuration?: number; // days
  requiresApproval: boolean;
  canAppeal: boolean;
}

export const RESTRICTION_TYPE_INFO: Record<RestrictionType, RestrictionTypeInfo> = {
  [RestrictionType.ACCOUNT_SUSPENSION]: {
    type: RestrictionType.ACCOUNT_SUSPENSION,
    label: 'Account Suspension',
    description: 'Complete suspension of account access and functionality',
    impact: [
      'Cannot log in to the platform',
      'All active bookings are cancelled',
      'Cannot access any platform features',
      'Profile is hidden from other users'
    ],
    requiresApproval: true,
    canAppeal: true
  },
  [RestrictionType.BOOKING_RESTRICTIONS]: {
    type: RestrictionType.BOOKING_RESTRICTIONS,
    label: 'Booking Restrictions',
    description: 'Prevents user from creating new service bookings',
    impact: [
      'Cannot create new bookings',
      'Cannot search for services',
      'Existing bookings remain active',
      'Can still communicate about existing bookings'
    ],
    defaultDuration: 30,
    requiresApproval: false,
    canAppeal: true
  },
  [RestrictionType.SERVICE_PROVIDER_RESTRICTIONS]: {
    type: RestrictionType.SERVICE_PROVIDER_RESTRICTIONS,
    label: 'Service Provider Restrictions',
    description: 'Prevents service providers from accepting new bookings',
    impact: [
      'Cannot accept new booking requests',
      'Profile hidden from search results',
      'Existing bookings remain active',
      'Cannot update service offerings'
    ],
    defaultDuration: 14,
    requiresApproval: false,
    canAppeal: true
  },
  [RestrictionType.COMMUNICATION_RESTRICTIONS]: {
    type: RestrictionType.COMMUNICATION_RESTRICTIONS,
    label: 'Communication Restrictions',
    description: 'Limits messaging and communication capabilities',
    impact: [
      'Cannot send new messages',
      'Cannot initiate conversations',
      'Can only respond to existing conversations',
      'Cannot post reviews or comments'
    ],
    defaultDuration: 7,
    requiresApproval: false,
    canAppeal: true
  },
  [RestrictionType.PAYMENT_RESTRICTIONS]: {
    type: RestrictionType.PAYMENT_RESTRICTIONS,
    label: 'Payment Restrictions',
    description: 'Requires manual approval for all payment transactions',
    impact: [
      'All payments require admin approval',
      'Cannot use automatic payment methods',
      'Refunds require manual processing',
      'Cannot update payment methods'
    ],
    defaultDuration: 30,
    requiresApproval: true,
    canAppeal: true
  },
  [RestrictionType.PROFILE_RESTRICTIONS]: {
    type: RestrictionType.PROFILE_RESTRICTIONS,
    label: 'Profile Restrictions',
    description: 'Prevents profile updates and limits visibility',
    impact: [
      'Cannot update profile information',
      'Cannot upload new photos',
      'Profile has limited visibility',
      'Cannot update service descriptions'
    ],
    defaultDuration: 14,
    requiresApproval: false,
    canAppeal: true
  },
  [RestrictionType.REVIEW_RESTRICTIONS]: {
    type: RestrictionType.REVIEW_RESTRICTIONS,
    label: 'Review Restrictions',
    description: 'Prevents posting reviews and ratings',
    impact: [
      'Cannot post new reviews',
      'Cannot rate services',
      'Cannot respond to reviews',
      'Existing reviews remain visible'
    ],
    defaultDuration: 30,
    requiresApproval: false,
    canAppeal: true
  }
};

export interface RestrictionReasonInfo {
  category: RestrictionReasonCategory;
  label: string;
  description: string;
  suggestedRestrictions: RestrictionType[];
  defaultSeverity: RestrictionSeverity;
}

export const RESTRICTION_REASON_INFO: Record<RestrictionReasonCategory, RestrictionReasonInfo> = {
  [RestrictionReasonCategory.POLICY_VIOLATION]: {
    category: RestrictionReasonCategory.POLICY_VIOLATION,
    label: 'Policy Violation',
    description: 'General violation of platform terms and policies',
    suggestedRestrictions: [RestrictionType.BOOKING_RESTRICTIONS, RestrictionType.COMMUNICATION_RESTRICTIONS],
    defaultSeverity: RestrictionSeverity.MODERATE
  },
  [RestrictionReasonCategory.INAPPROPRIATE_BEHAVIOR]: {
    category: RestrictionReasonCategory.INAPPROPRIATE_BEHAVIOR,
    label: 'Inappropriate Behavior',
    description: 'Rude, offensive, or unprofessional conduct',
    suggestedRestrictions: [RestrictionType.COMMUNICATION_RESTRICTIONS, RestrictionType.REVIEW_RESTRICTIONS],
    defaultSeverity: RestrictionSeverity.MODERATE
  },
  [RestrictionReasonCategory.SPAM_ACTIVITY]: {
    category: RestrictionReasonCategory.SPAM_ACTIVITY,
    label: 'Spam Activity',
    description: 'Sending unwanted promotional messages or repetitive content',
    suggestedRestrictions: [RestrictionType.COMMUNICATION_RESTRICTIONS, RestrictionType.PROFILE_RESTRICTIONS],
    defaultSeverity: RestrictionSeverity.MINOR
  },
  [RestrictionReasonCategory.FRAUDULENT_ACTIVITY]: {
    category: RestrictionReasonCategory.FRAUDULENT_ACTIVITY,
    label: 'Fraudulent Activity',
    description: 'Scams, fake services, or deceptive practices',
    suggestedRestrictions: [RestrictionType.ACCOUNT_SUSPENSION, RestrictionType.PAYMENT_RESTRICTIONS],
    defaultSeverity: RestrictionSeverity.CRITICAL
  },
  [RestrictionReasonCategory.PAYMENT_ISSUES]: {
    category: RestrictionReasonCategory.PAYMENT_ISSUES,
    label: 'Payment Issues',
    description: 'Problems with payments, chargebacks, or financial disputes',
    suggestedRestrictions: [RestrictionType.PAYMENT_RESTRICTIONS, RestrictionType.BOOKING_RESTRICTIONS],
    defaultSeverity: RestrictionSeverity.MODERATE
  },
  [RestrictionReasonCategory.SERVICE_QUALITY]: {
    category: RestrictionReasonCategory.SERVICE_QUALITY,
    label: 'Service Quality Issues',
    description: 'Poor service delivery or unmet expectations',
    suggestedRestrictions: [RestrictionType.SERVICE_PROVIDER_RESTRICTIONS, RestrictionType.REVIEW_RESTRICTIONS],
    defaultSeverity: RestrictionSeverity.MODERATE
  },
  [RestrictionReasonCategory.HARASSMENT]: {
    category: RestrictionReasonCategory.HARASSMENT,
    label: 'Harassment',
    description: 'Threatening, bullying, or persistent unwanted contact',
    suggestedRestrictions: [RestrictionType.COMMUNICATION_RESTRICTIONS, RestrictionType.ACCOUNT_SUSPENSION],
    defaultSeverity: RestrictionSeverity.SEVERE
  },
  [RestrictionReasonCategory.FAKE_PROFILE]: {
    category: RestrictionReasonCategory.FAKE_PROFILE,
    label: 'Fake Profile',
    description: 'Impersonation or false identity',
    suggestedRestrictions: [RestrictionType.ACCOUNT_SUSPENSION, RestrictionType.PROFILE_RESTRICTIONS],
    defaultSeverity: RestrictionSeverity.SEVERE
  },
  [RestrictionReasonCategory.REPEATED_VIOLATIONS]: {
    category: RestrictionReasonCategory.REPEATED_VIOLATIONS,
    label: 'Repeated Violations',
    description: 'Multiple violations despite previous warnings',
    suggestedRestrictions: [RestrictionType.ACCOUNT_SUSPENSION],
    defaultSeverity: RestrictionSeverity.SEVERE
  },
  [RestrictionReasonCategory.LEGAL_COMPLIANCE]: {
    category: RestrictionReasonCategory.LEGAL_COMPLIANCE,
    label: 'Legal Compliance',
    description: 'Required for legal or regulatory compliance',
    suggestedRestrictions: [RestrictionType.ACCOUNT_SUSPENSION],
    defaultSeverity: RestrictionSeverity.CRITICAL
  },
  [RestrictionReasonCategory.OTHER]: {
    category: RestrictionReasonCategory.OTHER,
    label: 'Other',
    description: 'Other violations not covered by specific categories',
    suggestedRestrictions: [RestrictionType.BOOKING_RESTRICTIONS],
    defaultSeverity: RestrictionSeverity.MODERATE
  }
};

// Helper functions
export function getRestrictionTypeInfo(type: RestrictionType): RestrictionTypeInfo {
  return RESTRICTION_TYPE_INFO[type];
}

export function getRestrictionReasonInfo(category: RestrictionReasonCategory): RestrictionReasonInfo {
  return RESTRICTION_REASON_INFO[category];
}

export function getSuggestedRestrictions(reasonCategory: RestrictionReasonCategory): RestrictionType[] {
  return RESTRICTION_REASON_INFO[reasonCategory].suggestedRestrictions;
}

export function getDefaultSeverity(reasonCategory: RestrictionReasonCategory): RestrictionSeverity {
  return RESTRICTION_REASON_INFO[reasonCategory].defaultSeverity;
}

export function getDefaultDuration(restrictionType: RestrictionType): number | undefined {
  return RESTRICTION_TYPE_INFO[restrictionType].defaultDuration;
}

export function requiresApproval(restrictionType: RestrictionType): boolean {
  return RESTRICTION_TYPE_INFO[restrictionType].requiresApproval;
}

export function canAppeal(restrictionType: RestrictionType): boolean {
  return RESTRICTION_TYPE_INFO[restrictionType].canAppeal;
}
